import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@langchain/cohere';
import { ChatTogetherAI } from '@langchain/community/chat_models/togetherai';
import { StringOutputParser } from '@langchain/core/output_parsers';
import {
  ChatPromptTemplate,
  HumanMessagePromptTemplate,
  SystemMessagePromptTemplate,
} from '@langchain/core/prompts';

// Import model mappings from the chat route
import { TOGETHER_MODELS, COHERE_MODELS, getModelProvider, getModelId } from '../chat/models';

// Initialize Pinecone client
const getPineconeClient = () => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings
const getEmbeddings = () => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0',
    inputType: 'search_query',
    truncate: 'END',
    embeddingFormat: 'float'
  });
};

// Get LLM based on selected model
const getLLM = (model: string) => {
  const provider = getModelProvider(model);
  const modelId = getModelId(model, provider);

  if (provider === 'cohere') {
    const apiKey = process.env.COHERE_API_KEY;
    if (!apiKey) {
      throw new Error('COHERE_API_KEY is not defined in environment variables');
    }

    return new ChatCohere({
      apiKey,
      model: modelId,
      temperature: 0.3,
      maxTokens: 2000,
    });
  } else {
    const apiKey = process.env.TOGETHER_API_KEY;
    if (!apiKey) {
      throw new Error('TOGETHER_API_KEY is not defined in environment variables');
    }

    return new ChatTogetherAI({
      apiKey,
      modelName: modelId,
      temperature: 0.3,
      maxTokens: 2000,
    });
  }
};

// Deep Research implementation
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const {
      messages,
      datasetId,
      pdfId,
      model = 'command-r-plus',
      namespace = 'adeloop',
      useNamespaceOnly = false,
      maxIterations = 3 // Default to 3 iterations for deep research
    } = body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Messages are required' }, { status: 400 });
    }

    // Get the last user message
    const lastUserMessage = messages[messages.length - 1];
    if (lastUserMessage.role !== 'user') {
      return NextResponse.json({ error: 'Last message must be from user' }, { status: 400 });
    }

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = process.env.PINECONE_INDEX || 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Create vector store
    const vectorStore = await PineconeStore.fromExistingIndex(embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
    });

    // Get LLM
    const llm = getLLM(model);

    // Initialize variables for deep research
    let currentQuery = lastUserMessage.content;
    let accumulatedContext = '';
    let allRetrievedDocs: any[] = [];
    let subQuestions: string[] = [];
    let iterationResults: any[] = [];

    // Deep Research: Perform multiple iterations of retrieval and reasoning
    for (let iteration = 0; iteration < maxIterations; iteration++) {
      console.log(`Deep Research Iteration ${iteration + 1}/${maxIterations} - Query: ${currentQuery}`);

      // Create filter based on dataset or PDF
      let metadataFilter = undefined;
      if (!useNamespaceOnly) {
        if (datasetId) {
          metadataFilter = { source: 'dataset', datasetId: datasetId };
        } else if (pdfId) {
          metadataFilter = { source: 'pdf', pdfId: pdfId };
        }
      }

      // Create retriever with proper configuration
      // For CSV datasets, increase the number of retrieved documents
      // This helps with finding specific values in large datasets
      const k = datasetId ? 10 : 5;

      const retriever = vectorStore.asRetriever({
        searchType: "similarity",
        k: k, // Retrieve more documents for CSV datasets
        filter: metadataFilter
      });

      // Retrieve documents for the current query
      const docs = await retriever.invoke(currentQuery);

      // Process documents to ensure they are strings
      const processedDocs = docs.map(doc => {
        if (typeof doc.pageContent !== 'string') {
          doc.pageContent = JSON.stringify(doc.pageContent);
        }
        return doc;
      });

      // Add to accumulated documents
      allRetrievedDocs = [...allRetrievedDocs, ...processedDocs];

      // Format documents for this iteration
      const formattedDocsText = processedDocs.map((doc, i) => {
        const source = doc.metadata?.source || 'unknown';
        let sourceInfo = '';

        if (source === 'pdf') {
          sourceInfo = `PDF: ${doc.metadata?.fileName || 'Unknown'} (Page ${doc.metadata?.page || 'N/A'})`;
        } else if (source === 'dataset') {
          // For CSV datasets, include more detailed information about the row
          sourceInfo = `Dataset: ${doc.metadata?.datasetName || 'Unknown'} (Row ${doc.metadata?.rowIndex !== undefined ? doc.metadata.rowIndex + 1 : 'N/A'})`;

          // If this is a CSV dataset, try to extract column names and values
          if (doc.metadata?.columns && Array.isArray(doc.metadata.columns)) {
            const columns = doc.metadata.columns;
            sourceInfo += ` - Columns: ${columns.join(', ')}`;
          }
        } else {
          sourceInfo = `Source: ${source}`;
        }

        return `Document ${i+1} [${sourceInfo}]: ${doc.pageContent}`;
      }).join('\n\n');

      // Add to accumulated context
      accumulatedContext += formattedDocsText + '\n\n';

      // If this is the last iteration, break out of the loop
      if (iteration === maxIterations - 1) {
        break;
      }

      // Send an interim response with the current iteration count
      // This would be ideal with server-sent events, but we'll use a workaround
      console.log(`Deep Research iteration ${iteration + 1}/${maxIterations} completed`);

      // Generate sub-questions for the next iteration
      const subQuestionPrompt = ChatPromptTemplate.fromMessages([
        SystemMessagePromptTemplate.fromTemplate(
          `You are an AI research assistant that helps generate follow-up questions to deepen the research.
          Based on the original question and the information retrieved so far, generate 1-3 specific follow-up questions
          that would help gather more relevant information to answer the original question comprehensively.
          Focus on aspects that haven't been covered yet or need more details.
          Return ONLY the follow-up questions, one per line, with no additional text or explanations.`
        ),
        HumanMessagePromptTemplate.fromTemplate(
          `Original question: ${lastUserMessage.content}

          Information retrieved so far:
          ${formattedDocsText}

          Generate 1-3 follow-up questions to deepen the research:`
        ),
      ]);

      const subQuestionResponse = await llm.invoke(await subQuestionPrompt.format({}));
      const subQuestionText = typeof subQuestionResponse.content === 'string'
        ? subQuestionResponse.content
        : JSON.stringify(subQuestionResponse.content);

      // Parse sub-questions (one per line)
      const newSubQuestions = subQuestionText
        .split('\n')
        .map(q => q.trim())
        .filter(q => q && q.length > 10 && (q.endsWith('?') || q.includes('?')));

      // If we couldn't generate valid sub-questions, break out of the loop
      if (newSubQuestions.length === 0) {
        console.log('No valid sub-questions generated, ending deep research');
        break;
      }

      // Add to sub-questions list
      subQuestions = [...subQuestions, ...newSubQuestions];

      // Use the first sub-question as the next query
      currentQuery = newSubQuestions[0];

      // Store iteration results
      iterationResults.push({
        iteration: iteration + 1,
        query: currentQuery,
        docsRetrieved: processedDocs.length,
        subQuestions: newSubQuestions
      });

      console.log(`Generated sub-questions for next iteration:`, newSubQuestions);
    }

    // Final synthesis with all accumulated context
    const finalPrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(
        `You are an AI assistant that answers questions based on the provided context.
        Use ONLY the information from the context to answer the question.
        If you don't know the answer based on the context, say "I don't have enough information in the provided data to answer this question."
        Always include relevant details from the context in your answer.
        Be specific and reference the source of information in your answer (e.g., "According to the dataset..." or "As mentioned in the PDF...").

        The context contains information from datasets and/or PDF documents that the user has selected.
        Each piece of context is labeled with its source.

        IMPORTANT INSTRUCTIONS FOR CSV DATA:
        - When working with CSV data, pay special attention to exact values and numbers
        - If asked about specific IDs, employee numbers, or exact values, search carefully through all rows
        - For numerical questions, provide precise answers with calculations when needed
        - If you can't find an exact match for a specific ID or value, say so clearly
        - When analyzing CSV data, consider all relevant columns and rows before answering

        IMPORTANT: You must ONLY use the information in the context below. Do NOT use any prior knowledge.
        If the context doesn't contain relevant information, admit that you don't have enough information.

        Context:
        ${accumulatedContext}`
      ),
      HumanMessagePromptTemplate.fromTemplate(lastUserMessage.content),
    ]);

    // Execute LLM with the final prompt
    const llmResponse = await llm.invoke(await finalPrompt.format({}));

    // Extract the text content
    let response = '';
    if (typeof llmResponse.content === 'string') {
      response = llmResponse.content;
    } else {
      response = JSON.stringify(llmResponse.content);
    }

    // Prepare source documents for the response
    const sourceDocuments = allRetrievedDocs.map((doc, i) => ({
      content: doc.pageContent,
      metadata: doc.metadata,
      index: i + 1
    }));

    // Return the response with deep research metadata
    return NextResponse.json({
      content: response,
      model: model,
      provider: getModelProvider(model),
      sourceDocuments: sourceDocuments,
      deepResearch: {
        iterations: iterationResults,
        totalIterations: iterationResults.length + 1,
        subQuestions: subQuestions
      }
    });
  } catch (error: any) {
    console.error('Error in Deep Research API:', error);
    return NextResponse.json({
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
