@import url('https://fonts.googleapis.com/css2?family=Pacifico&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 0 0% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 0 0% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 3.9%;
		--primary: 0 0% 9%;
		--primary-foreground: 0 0% 98%;
		--secondary: 0 0% 96.1%;
		--secondary-foreground: 0 0% 9%;
		--muted: 0 0% 96.1%;
		--support: 208.24 2.83% 42.65%;
		--muted-foreground: 0 0% 55.1%;
		--accent: 0 0% 96.1%;
		--accent-foreground: 0 0% 9%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 89.8%;

		--input-background: 0 0% 98%;
		--input: 0 0% 91.8%;

		--ring: 0 0% 60%;
		--label: 0 0% 50%;
		--radius: 0.6rem;

		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;

		--gradient-text: linear-gradient(to right, #4F46E5, #9333EA, #EC4899);

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --sidebar-width: 0px;
	}

	.dark {
		--background: 0 0% 3.9%;
		--foreground: 0 0% 98%;
		--card: 0 0% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 0 0% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 0 0% 9%;
		--secondary: 0 0% 14.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 0 0% 14.9%;
		--support: 208.24 2.83% 42.65%;
		--muted-foreground: 0 0% 63.9%;
		--accent: 0 0% 14.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 14.9%;
		--input: 0 0% 14.9%;
		--ring: 0 0% 83.1%;
		--label: 0 0% 20%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}

	@font-face {
		font-family: 'Pacifico', cursive;
		font-style: normal;
	}

	input[type='number']::-webkit-inner-spin-button,
	input[type='number']::-webkit-outer-spin-button {
		-webkit-appearance: none;
	}
}


/* Add to your globals.css */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #e2e8f0;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: #cbd5e1;
  }
  /* Add to your globals.css */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175 / 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175 / 0.7);
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background: transparent;
}

  /* Modern AI Chat Animations */
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0px); }
  }

  @keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  @keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
  }

  @keyframes pulse-ring {
    0% { transform: scale(0.8); opacity: 0.8; }
    50% { transform: scale(1.2); opacity: 0.4; }
    100% { transform: scale(0.8); opacity: 0.8; }
  }

  @keyframes fade-in {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
  }

  @keyframes slide-in {
    0% { transform: translateX(-20px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
  }

  @keyframes scale-in {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes typing {
    0% { width: 0; }
    100% { width: 100%; }
  }

  @keyframes progress {
    0% { width: 0%; background-position: 0% 50%; }
    50% { width: 100%; background-position: 100% 50%; }
    100% { width: 0%; background-position: 0% 50%; }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 3s linear infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s ease-in-out infinite;
  }

  .animate-pulse-ring {
    animation: pulse-ring 3s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
  }

  .animate-fade-in {
    animation: fade-in 0.5s ease-out forwards;
  }

  .animate-slide-in {
    animation: slide-in 0.5s ease-out forwards;
  }

  .animate-scale-in {
    animation: scale-in 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-typing {
    animation: typing 2s steps(40, end);
  }
}



/* Add these classes at the end of your globals.css file */

.mini-sidebar-margin {
  margin-left: 0;
  transition: all 0.3s ease-in-out;
  padding: 1.5rem;
}

.mini-sidebar-open .mini-sidebar-margin {
  margin-left: 11rem; /* 256px - width of mini sidebar */
}

.mini-sidebar-open {
  transition: all 0.3s ease-in-out;
}

@media print {
  body * {
    visibility: hidden;
  }
  .print-view, .print-view * {
    visibility: visible;
  }
  .print-view {
    position: absolute;
    left: 0;
    top: 0;
  }
}

@media print {
  body {
    background: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .print-view {
    display: block !important;
    visibility: visible !important;
    background: white !important;
  }

  .print-chart {
    background-color: white !important;
    padding: 8px !important;
  }

  .print-chart svg {
    height: 200px !important;
    width: 100% !important;
  }

  .print-chart text {
    fill: black !important;
    font-family: Arial, sans-serif !important;
    font-weight: bold !important;
  }

  .print-chart path,
  .print-chart line {
    stroke: black !important;
  }

  .print-chart-wrapper {
    border: 1px solid black !important;
    padding: 8px !important;
    background: white !important;
  }

  .recharts-legend-item-text {
    color: black !important;
  }

  .recharts-cartesian-axis-line {
    stroke: black !important;
  }

  .recharts-cartesian-grid line {
    stroke: #666666 !important;
  }
}

@media print {
  #pdf-content {
    display: block !important;
    visibility: visible !important;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }

  #pdf-content * {
    visibility: visible !important;
  }

  .recharts-wrapper {
    width: 100% !important;
  }

  .recharts-surface {
    width: 100% !important;
  }

  .print-chart {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
}


::-webkit-scrollbar-thumb {

  border-radius: 10px;
}

::-webkit-scrollbar-track {

}

img {
  user-select: none;
  pointer-events: none;
}

.header {
  --clip: inset(0 0 calc(100% - 48px + 8px) 0 round 16px);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  isolation: isolate;
  overflow: hidden;
  margin-inline: auto;
  transform: translateX(calc(-1 * 5px / 2));
  transition: .24s var(--ease-out-quad);
  transition-property: background, -webkit-clipPath;
  transition-property: clipPath, background;
  transition-property: clipPath, background, -webkit-clipPath;
}

.headyer::before {
  content: "";
  position: absolute;
  pointer-events: none;
  inset: 0;
  border: 1px solid hsl(var(--border));
  border-radius: inherit;
  height: calc(64px - 16px);
  will-change: height;
  transition: inherit;
  transition-property: height;
}

.btn-primary {
  z-index: 20;
  /* background-image: linear-gradient(to right, hsl(var(--primary)), #9333ea); */
  color: #fff;
  text-align: center;
  background-image: radial-gradient(circle farthest-side at 30% 0, rgba(255, 255, 255, .12), transparent);
  box-shadow: inset 1px 1px 2px rgba(255, 255, 255, .24), 0 1px 3px hsl(var(--primary)/0.24), 0 2px 6px hsl(var(--primary)/0.24), 0 4px 8px rgba(96, 10, 255, 0.12), 0 16px 32px -8px hsl(var(--primary)/0.48)
}

.btn-primary:hover {
  background-color: #7c3aed;
  color: #fff;
  transform: scale(1.05) translateY(-4px);
  box-shadow: inset 0 0 rgba(255, 255, 255, 0.24),
    0 1px 3px rgba(124, 58, 237, 0.24),
    0 2px 6px rgba(124, 58, 237, 0.24),
    0 4px 8px rgba(124, 58, 237, 0.12),
    0 20px 40px -8px rgba(124, 58, 237, 0.64);
}

.btn-primary:active {
  background-color: #7c3aed;
  transform: scale(1) translate(0);
  box-shadow: inset 0 0 rgba(255, 255, 255, 0.24),
    0 1px 3px rgba(124, 58, 237, 0.48),
    0 2px 6px rgba(124, 58, 237, 0.48),
    0 4px 8px rgba(124, 58, 237, 0.48),
    0 4px 12px -8px rgba(124, 58, 237, 1);
}

.btn-secondary {
  z-index: 20;
  background-color: hsl(var(--background)/0.04);
  color: #fff;
  text-align: center;
  background-image: radial-gradient(circle farthest-side at 35% -50%, rgba(255, 255, 255, .08), rgba(255, 255, 255, 0));
  box-shadow: 0 8px 40px -20px rgba(255, 255, 255, .2),
    inset 1px 1px rgba(255, 255, 255, .08),
    inset 0 0 0 1px rgba(255, 255, 255, .06);
}

.btn-secondary:hover {
  background-color: hsl(var(--background)/0.08);
  color: #fff;
  transform: scale(1.05) translateY(-4px);
  box-shadow: 0 8px 40px -20px rgba(255, 255, 255, .32),
    inset 1px 1px rgba(255, 255, 255, .08),
    inset 0 0 0 1px rgba(255, 255, 255, .1);
}

.btn-secondary:active {
  background-color: hsl(var(--background)/0.08);
  transform: scale(1) translateY(0);
  box-shadow: 0 8px 40px -20px rgba(255, 255, 255, .32),
    inset 1px 1px rgba(255, 255, 255, .08),
    inset 0 0 0 1px rgba(255, 255, 255, .1);
}

.badge {
  box-shadow: 0 0 0 1px hsl(var(--primary));
}

.heading {
  @apply bg-gradient-to-b from-foreground to-foreground/60 bg-clip-text text-transparent;
}

.bento-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  border-radius: 0.75rem;
  position: relative;
  z-index: 50;
}

@media screen and (min-width: 768px) {
  .bento-card {
    padding: 1.5rem;
  }
}

.spotlight::after {
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  inset: 0;
  z-index: 10;
  background: radial-gradient(200px circle at var(--mouse-x) var(--mouse-y), hsl(var(--foreground)), transparent);
  transition: background 0.3s ease;
}

.group:hover .spotlight::after {
  border-color: #fff;
}

:root {
  --content-background: #100F1B;
  --spot-light-size: 800px;
  --spot-light-color: rgba(139, 92, 246, 0.15);
  --card-border-color: rgba(255, 255, 255, 0.4);
}

.card {
  background-color: rgba(255, 255, 255, 0.1);
  height: 100%;
  width: 100%;
  position: relative;
}

.content {
  background: var(--content-background);
  height: calc(100% - 2px);
  width: calc(100% - 2px);
  transform: translate(1px, 1px);
  border-radius: inherit;
}

.card:before,
.card:after {
  content: '';
  position: absolute;
  inset: 0;
  transition: opacity 500ms cubic-bezier(0.075, 0.82, 0.165, 1);
  border-radius: inherit;
  opacity: 0;
}

/* spotlight */
.card:after {
  background: radial-gradient(var(--spot-light-size) circle at var(--pos-x) var(--pos-y),
      var(--spot-light-color),
      transparent 40%);
}

/* card's border */
.card:before {
  background: radial-gradient(calc(var(--spot-light-size) / 2) circle at var(--pos-x) var(--pos-y),
      var(--card-border-color),
      transparent 40%);
}

.card:hover:after,
.card:hover:before {
  opacity: 1;
}

.pricing {
  background: radial-gradient(ellipse 80% 50% at 50% -20%, rgba(0, 24, 51, .6), rgba(25, 8, 43, .3));
}

.footer::before {
  background: radial-gradient(50% 56400% at 50% 100%, rgba(40, 34, 57, 0.2) 0%, rgba(169, 163, 194, 0) 100%);
  bottom: 0;
  content: "";
  height: 1px;
  left: 0;
  position: absolute;
  width: 100%;
  color: rgb(23, 23, 23);
}

.th {
  transition: height 0.3s ease;
}

.gradient {
  background: conic-gradient(from 230.29deg at 51.63% 52.16%, rgb(36, 0, 255) 0deg, rgb(0, 135, 255) 67.5deg, rgb(108, 39, 157) 198.75deg, rgb(24, 38, 163) 251.25deg, rgb(54, 103, 196) 301.88deg, rgb(105, 30, 255) 360deg);
}

.gradient-text {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.employee-card {
  @apply relative overflow-hidden;
}

.employee-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom right,
    rgba(var(--primary-rgb), 0.1),
    rgba(var(--primary-rgb), 0.05)
  );
  pointer-events: none;
}

.info-section-title {
  @apply bg-gradient-to-r from-primary via-primary/80 to-primary/60
         bg-clip-text text-transparent font-semibold text-lg;
}

body {
  --sidebar-width: 4rem;
}

/* Update sidebar open state */
body.sidebar-open {
  --mini-sidebar-width: 16rem;
}

/* Content container with proper transitions */
.content-container {
  width: 100%;
  padding-left: calc(var(--sidebar-width) + var(--mini-sidebar-width));
  transition: padding-left 300ms ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --sidebar-width: 0;
    --mini-sidebar-width: 0;
  }

  .content-container {
    padding-left: 1rem;
  }
}

/* Sidebar transition styles */
.sidebar-content-wrapper {
  margin-left: 0;
  width: 100%;
  transition: margin-left 300ms ease-in-out;
}

body.sidebar-expanded .sidebar-content-wrapper {
  margin-left: 16rem;
}

@media (max-width: 768px) {
  body.sidebar-expanded .sidebar-content-wrapper {
    margin-left: 0;
  }
}

.content-wrapper {
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease-in-out;
}

/* Employee Mini Sidebar Styles */
.employee-mini-sidebar {
  position: fixed;
  left: 4rem; /* Default position - will be overridden by inline style */
  top: 0;
  height: 100vh;
  width: 12rem; /* Reduced width when open */
  z-index: 50;
  transform: translateX(-100%);
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.employee-mini-sidebar.open {
  transform: translateX(0);
  width: 12rem; /* Reduced width when open */
}

/* When sidebar is closed, show a narrow version with icons */
.employee-mini-sidebar:not(.open) {
  transform: translateX(0);
  width: 3.5rem; /* Reduced width for icon-only mode */
  overflow: hidden;
  padding: 0.5rem 0;
}

/* Style for icons in closed sidebar */
.employee-mini-sidebar:not(.open) nav ul li a {
  width: 2.25rem;
  height: 2.25rem;
  margin: 0.25rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Adjust for main sidebar expanded state */
.employee-mini-sidebar.main-sidebar-expanded {
  /* Position is handled by inline style */
  box-shadow: 1px 0 5px rgba(0, 0, 0, 0.08);
}

.employee-mini-sidebar-toggle {
  position: fixed;
  /* left position is handled by inline style */
  top: 0.75rem;
  z-index: 51;
  width: 1.25rem; /* Smaller button */
  height: 1.25rem; /* Smaller button */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

.employee-mini-sidebar-toggle:hover {
  background-color: hsl(var(--accent));
  transform: scale(1.05);
}

.employee-mini-sidebar-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 40;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
}

.employee-mini-sidebar-overlay.open {
  opacity: 1;
  pointer-events: auto;
}

/* Responsive styles for mini sidebar */
@media (max-width: 1280px) {
  .employee-mini-sidebar {
    width: 12rem; /* Slightly smaller on medium screens */
  }

  .employee-mini-sidebar.open {
    width: 12rem;
  }

  .employee-mini-sidebar:not(.open) {
    width: 3.25rem; /* Slightly smaller on medium screens */
  }
}

@media (max-width: 1024px) {
  .employee-mini-sidebar {
    width: 10rem; /* Even smaller on smaller screens */
  }

  .employee-mini-sidebar.open {
    width: 10rem;
  }

  .employee-mini-sidebar:not(.open) {
    width: 3rem; /* Even smaller on smaller screens */
  }
}

@media (max-width: 768px) {
  .employee-mini-sidebar {
    display: none; /* Hide on mobile */
  }

  .employee-mini-sidebar-toggle {
    display: none; /* Hide toggle on mobile */
  }
}

/* Ensure main content resizes properly with sidebar */
main {
  width: 100%;
  max-width: 100vw;
  transition: margin-left 0.3s ease-in-out;
  overflow-x: hidden;
}

/* Prevent horizontal scrolling but allow vertical scrolling */
html, body {
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Fix for sidebar open state to ensure page scrolling works */
html.sidebar-open, body.sidebar-open {
  overflow-y: auto !important;
}

