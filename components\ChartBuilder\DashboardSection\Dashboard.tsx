'use client'

/**
 * Dashboard Component
 *
 * The main dashboard component for HRatlas that brings together all dashboard functionality.
 * This component renders a complete dashboard with toolbar, layout, and items.
 *
 * Features:
 * - Drag and drop layout of charts, headings, and text cards
 * - Edit mode for rearranging items
 * - Export and print functionality
 * - Auto-arranged layouts
 */

// Extend Window interface to add our custom properties
declare global {
  interface Window {
    saveLayoutTimeout?: number;
    dashboardOperationInProgress?: boolean;
    isDraggingDashboardItem?: boolean;
    isResizingDashboardItem?: boolean;
    activeItemId?: string;
    lastLayoutUpdate?: number;
  }
}

import { useEffect, useState, useCallback, useRef } from 'react';
import { DashboardLayout } from './DashboardLayout';
import { DashboardToolbar } from './DashboardToolbar';
import { SavedChart, DashboardItem, HeadingItem, TextItem } from './types';
import { toast } from 'sonner';
import { useTheme } from 'next-themes';
import html2canvas from 'html2canvas';
import { useDashboardStore } from '@/lib/dashboardStore';
// Import styles
import './styles/dashboard-enhanced.css';

interface DashboardProps {
  items?: DashboardItem[];
  charts?: SavedChart[];
  onRemoveItem: (itemId: string) => void;
  onUpdateItem?: (itemId: string, updatedItem: Partial<DashboardItem>) => void;
  onReorderItems?: (newItems: DashboardItem[]) => void;
  onAddItem?: (item: DashboardItem) => void;
  // Legacy props for backward compatibility
  onRemoveChart?: (chartId: string) => void;
  onUpdateChart?: (chartId: string, updatedChart: Partial<SavedChart>) => void;
  onReorderCharts?: (newCharts: SavedChart[]) => void;
  onAddChart?: (chart: SavedChart) => void;
}

// Export as default for dynamic import
function DashboardComponent({
  items,
  charts,
  onRemoveItem,
  onUpdateItem,
  onReorderItems,
  onAddItem,
  onRemoveChart,
  onUpdateChart,
  onReorderCharts,
  onAddChart
}: DashboardProps) {
  // Convert legacy charts to items if needed
  const initialItems: DashboardItem[] = items ||
    (charts ? charts.map(chart => ({ ...chart, type: 'chart' as const })) : []);

  const [orderedItems, setOrderedItems] = useState<DashboardItem[]>(initialItems);
  const [isEditMode, setIsEditMode] = useState(true); // Start in edit mode by default
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { theme } = useTheme();
  const [isInitialized, setIsInitialized] = useState(false);
  const dashboardRef = useRef<HTMLDivElement>(null);

  // Use a ref to store the last saved layout to prevent unnecessary updates
  const lastSavedLayoutRef = useRef<string>("");
  const operationInProgressRef = useRef<boolean>(false);

  // Initialize with initial items
  useEffect(() => {
    if (!isInitialized) {
      try {
        if (initialItems.length > 0) {
          setOrderedItems(initialItems);
        }
      } catch (error) {
        console.error('Error initializing dashboard:', error);
      } finally {
        setIsInitialized(true);
      }
    }
  }, [initialItems, isInitialized]);

  // Simple function to log the current layout (replaced forceSaveLayout)
  const logLayout = useCallback(() => {
    console.log('Dashboard items:', orderedItems.length);
  }, [orderedItems]);

  // Function to refresh available items from the Zustand store
  const refreshAvailableItems = useCallback(() => {
    try {
      // Skip refresh if a drag or resize operation is in progress for better performance
      if (window.dashboardOperationInProgress ||
          window.isDraggingDashboardItem ||
          window.isResizingDashboardItem ||
          operationInProgressRef.current) {
        return;
      }

      // Get charts, tables, plots, and calculator results from the Zustand store
      const { charts: storeCharts, tables: storeTables, plots: storePlots, calculatorResults: storeCalculatorResults } = useDashboardStore.getState();

      // Add new items to the dashboard that aren't already there
      setOrderedItems(prevItems => {
        const currentIds = new Set(prevItems.map(item => item.id));
        let hasNewItems = false;

        // Start with existing items - this preserves calculator results and other items
        const newItems = [...prevItems];

        // Calculate the next available row for each new item
        let currentMaxRow = prevItems.length > 0
          ? Math.max(...prevItems.map(item => (item.gridRow || 0) + (item.height || 1)))
          : 0;

        // Process charts
        if (storeCharts && storeCharts.length > 0) {
          storeCharts.forEach(chart => {
            if (!currentIds.has(chart.id)) {
              hasNewItems = true;

              // Ensure we have valid grid positions and dimensions
              newItems.push({
                ...chart,
                gridRow: currentMaxRow,
                gridColumn: 0,
                width: chart.width || 4,
                height: chart.height || 3,
                type: 'chart' as const
              });

              // Update max row for next item
              currentMaxRow += (chart.height || 3);

              // Show toast notification
              toast.success(`Chart "${chart.title || 'Untitled'}" added to dashboard`, {
                duration: 2000
              });
            }
          });
        }

        // Process tables
        if (storeTables && storeTables.length > 0) {
          storeTables.forEach(table => {
            if (!currentIds.has(table.id)) {
              hasNewItems = true;

              // Ensure we have valid grid positions and dimensions
              newItems.push({
                ...table,
                gridRow: currentMaxRow,
                gridColumn: 0,
                width: table.width || 6,
                height: table.height || 4,
                type: 'table' as const
              });

              // Update max row for next item
              currentMaxRow += (table.height || 4);

              // Show toast notification
              toast.success(`Table added to dashboard`, {
                duration: 2000
              });
            }
          });
        }

        // Process plots
        if (storePlots && storePlots.length > 0) {
          storePlots.forEach(plot => {
            if (!currentIds.has(plot.id)) {
              hasNewItems = true;

              // Ensure we have valid grid positions and dimensions
              newItems.push({
                ...plot,
                gridRow: currentMaxRow,
                gridColumn: 0,
                width: plot.width || 6,
                height: plot.height || 4,
                type: 'pythonplot' as const
              });

              // Update max row for next item
              currentMaxRow += (plot.height || 4);

              // Show toast notification
              toast.success(`Plot added to dashboard`, {
                duration: 2000
              });
            }
          });
        }

        // Process calculator results
        if (storeCalculatorResults && storeCalculatorResults.length > 0) {
          storeCalculatorResults.forEach(calculatorResult => {
            if (!currentIds.has(calculatorResult.id)) {
              hasNewItems = true;

              // Ensure we have valid grid positions and dimensions
              newItems.push({
                ...calculatorResult,
                gridRow: currentMaxRow,
                gridColumn: 0,
                width: calculatorResult.width || 3, // Calculator cards are smaller
                height: calculatorResult.height || 3,
                type: 'calculator' as const
              });

              // Update max row for next item
              currentMaxRow += (calculatorResult.height || 3);

              // Show toast notification
              toast.success(`Calculator result "${calculatorResult.title || 'Untitled'}" added to dashboard`, {
                duration: 2000
              });
            }
          });
        }

        return hasNewItems ? newItems : prevItems;
      });
    } catch (error) {
      console.error('Error refreshing dashboard items:', error);
    }
  }, []);

  // Update local state when items or charts prop changes, but preserve local additions
  useEffect(() => {
    try {
      if (!isInitialized) {
        // First initialization
        if (items) {
          setOrderedItems(items);
        } else if (charts) {
          setOrderedItems(charts.map(chart => ({ ...chart, type: 'chart' as const })));
        }

        setIsInitialized(true);
        return;
      }

      if (operationInProgressRef.current) {
        return; // Skip updates during operations
      }

      if (items) {
        // Instead of replacing the entire state, merge the incoming items with local additions
        setOrderedItems(prevItems => {
          // Get IDs of all current items
          const currentIds = new Set(prevItems.map(item => item.id));

          // Filter out items that are already in our state (to preserve local changes)
          const newItems = items.filter(item => !currentIds.has(item.id));

          // Combine existing items with new items
          return [...prevItems, ...newItems];
        });
      } else if (charts) {
        // Same approach for charts - but preserve ALL existing items, not just charts
        setOrderedItems(prevItems => {
          console.log('🔄 Processing charts update. Current items:', prevItems.map(item => `${item.type}:${item.id}`));

          // Get IDs of current charts to avoid duplicates
          const currentChartIds = new Set(prevItems.filter(item => item.type === 'chart').map(item => item.id));

          // Start with ALL existing items (including calculator results, tables, etc.)
          const updatedItems = [...prevItems];

          // Calculate next available position for new charts
          let currentMaxRow = prevItems.length > 0
            ? Math.max(...prevItems.map(item => (item.gridRow || 0) + (item.height || 1)))
            : 0;

          // Process each incoming chart
          charts.forEach(chart => {
            const chartWithType = { ...chart, type: 'chart' as const };

            // Find existing chart in our items
            const existingIndex = updatedItems.findIndex(item => item.id === chart.id);

            if (existingIndex >= 0) {
              // Update existing chart but preserve position and size
              const existingItem = updatedItems[existingIndex];
              updatedItems[existingIndex] = {
                ...chartWithType,
                // Preserve layout properties
                gridColumn: existingItem.gridColumn,
                gridRow: existingItem.gridRow,
                width: existingItem.width,
                height: existingItem.height
              };
            } else if (!currentChartIds.has(chart.id)) {
              // This is a new chart, add it at the end
              updatedItems.push({
                ...chartWithType,
                gridRow: currentMaxRow,
                gridColumn: 0,
                width: chart.width || 4,
                height: chart.height || 3
              });
              currentMaxRow += (chart.height || 3);
            }
          });

          console.log('✅ Final items after charts update:', updatedItems.map(item => `${item.type}:${item.id}`));
          return updatedItems;
        });
      }
    } catch (error) {
      console.error('Error updating items:', error);
      toast.error('Error loading dashboard items');
    }
  }, [items, charts, isInitialized]);

  // Subscribe to store changes for all item types
  useEffect(() => {
    const unsubscribe = useDashboardStore.subscribe((state, prevState) => {
      // Check if any store items changed
      const hasChanges =
        state.calculatorResults.length !== prevState.calculatorResults.length ||
        state.charts.length !== prevState.charts.length ||
        state.tables.length !== prevState.tables.length ||
        state.plots.length !== prevState.plots.length;

      if (hasChanges) {
        refreshAvailableItems();
      }
    });

    return unsubscribe;
  }, [refreshAvailableItems]);

  // Populate available items from cell results store only once on initial load
  // This prevents duplicate charts from being added repeatedly
  useEffect(() => {
    // Only check for charts once when the dashboard is first mounted
    // This prevents the periodic refresh from adding duplicate charts
    if (charts && charts.length > 0) {
      // If we already have charts from props, don't check the store
      return;
    }

    // Do a single refresh to get any charts from the store
    refreshAvailableItems();

    // We no longer set up periodic refreshes or observers
    // Charts will only be added when explicitly saved from the ChartBuilder

  }, [refreshAvailableItems, charts]);


  // Handle item updates with debouncing to improve performance
  const handleUpdateItem = (itemId: string, updates: Partial<DashboardItem>) => {
    try {
      // Skip updates if a drag or resize operation is in progress
      if (window.dashboardOperationInProgress ||
          window.isDraggingDashboardItem ||
          window.isResizingDashboardItem) {
        return;
      }

      // Set local operation flag
      operationInProgressRef.current = true;

      // Update with a functional state update to avoid stale references
      setOrderedItems(prevItems => {
        // Skip update if item doesn't exist
        if (!prevItems.some(item => item.id === itemId)) {
          return prevItems;
        }

        // Create new array with updated item
        const updatedItems = prevItems.map(item => {
          if (item.id === itemId) {
            return { ...item, ...updates } as DashboardItem;
          }
          return item;
        });

        return updatedItems;
      });

      // Defer parent handler calls to avoid synchronous update cycles
      if (onUpdateItem) {
        setTimeout(() => {
          onUpdateItem(itemId, updates);
          operationInProgressRef.current = false;
        }, 50);
      } else if (onUpdateChart && updates.type !== 'heading' && updates.type !== 'text') {
        // Legacy support for charts only
        setTimeout(() => {
          onUpdateChart(itemId, updates as Partial<SavedChart>);
          operationInProgressRef.current = false;
        }, 50);
      } else {
        operationInProgressRef.current = false;
      }
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error('Failed to update item');
      operationInProgressRef.current = false;
    }
  };

  // Handle saving layout changes - SIMPLIFIED
  const handleSaveLayout = useCallback((updatedItems: DashboardItem[]) => {
    try {
      // Always update state - simpler and more reliable
      setOrderedItems(updatedItems);

      // Debounce parent handler calls
      if (window.saveLayoutTimeout) {
        clearTimeout(window.saveLayoutTimeout);
      }

      // Use setTimeout to defer parent updates
      window.saveLayoutTimeout = setTimeout(() => {
        if (onReorderItems) {
          onReorderItems(updatedItems);
        } else if (onReorderCharts) {
          // Legacy support
          const chartItems = updatedItems.filter(item => item.type === 'chart') as SavedChart[];
          onReorderCharts(chartItems);
        }
      }, 100) as unknown as number;
    } catch (error) {
      console.error('Error saving layout:', error);
    }
  }, [onReorderItems, onReorderCharts]);

  // Removed effect that saved layout to localStorage

  // Toggle edit mode
  const handleToggleEditMode = () => {
    // Log layout before toggling
    logLayout();

    setIsEditMode(prev => !prev);

    // Remove any stale guidelines when toggling edit mode
    document.querySelectorAll('.snap-guideline').forEach(el => el.remove());
    document.querySelectorAll('.position-indicator').forEach(el => el.remove());

    // Reset flags when toggling edit mode
    window.isDraggingDashboardItem = false;
    window.isResizingDashboardItem = false;
    window.dashboardOperationInProgress = false;
  };

  const handleExport = async () => {
    try {
      const dashboardElement = document.querySelector('.dashboard-container');
      if (!dashboardElement) return;

      // Set a temporary class for better export quality
      dashboardElement.classList.add('exporting');

      // Toggle to view mode for cleaner export
      const wasEditMode = isEditMode;
      if (wasEditMode) setIsEditMode(false);

      // Need a small delay to let the UI update
      await new Promise(resolve => setTimeout(resolve, 300));

      const canvas = await html2canvas(dashboardElement as HTMLElement, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        backgroundColor: theme === 'dark' ? '#1a1a1a' : '#ffffff'
      });

      // Create download link
      const link = document.createElement('a');
      link.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();

      // Restore edit mode if needed
      if (wasEditMode) setIsEditMode(true);
      dashboardElement.classList.remove('exporting');

      toast.success('Dashboard exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export dashboard');
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleAddChart = () => {
    // This now relies on the ChartBuilder to save charts to the dashboard
    toast.info('Create and save a chart from the Notebook tab to add it to the dashboard');
  };

  const handleAddHeading = (heading: HeadingItem) => {
    try {
      // Calculate the next available grid position
      const findNextPosition = () => {
        // If no items, start at the top
        if (orderedItems.length === 0) {
          return { gridColumn: 0, gridRow: 0 };
        }

        // Find the maximum row used
        const maxRow = Math.max(...orderedItems.map(item => (item.gridRow || 0) + (item.height || 1)));

        // For headings, start at the left edge of a new row
        return { gridColumn: 0, gridRow: maxRow };
      };

      const { gridColumn, gridRow } = findNextPosition();

      // Create a new heading item
      const newHeading: HeadingItem = {
        ...heading,
        id: heading.id,
        type: 'heading',
        gridColumn,
        gridRow,
        width: heading.width || 12, // Headings are usually full width
        height: heading.height || 1, // Headings are usually 1 unit tall
        createdAt: new Date(),
        isNew: true, // Mark as new so it enters edit mode
      };

      // Add the new heading
      setOrderedItems(prev => {
        const updatedItems = [...prev, newHeading];
        // Layout no longer saved to localStorage
        return updatedItems;
      });

      // Notify parent components
      if (onAddItem) {
        setTimeout(() => onAddItem(newHeading), 0);
      }

      toast.success('Heading added to dashboard');
    } catch (error) {
      console.error('Failed to add heading:', error);
      toast.error('Failed to add heading');
    }
  };

  const handleAddText = (isTitleStyle = false) => {
    try {
      // Generate a unique ID
      const id = `text-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      // Find the next suitable position based on existing items
      const findNextPosition = () => {
        // If no items, start at the top
        if (orderedItems.length === 0) {
          return { gridColumn: 0, gridRow: 0 };
        }

        // Find the maximum row used
        const maxRow = Math.max(...orderedItems.map(item => (item.gridRow || 0) + (item.height || 1)));

        // Attempt to find empty space in existing rows first for compact layout
        // Calculate grid occupancy
        const grid: boolean[][] = [];
        const columnCount = 12; // Assuming a 12-column grid

        // Initialize grid
        for (let i = 0; i <= maxRow + 5; i++) {
          grid[i] = Array(columnCount).fill(false);
        }

        // Mark occupied cells
        orderedItems.forEach(item => {
          const { gridColumn, gridRow, width, height } = item;
          for (let r = gridRow; r < gridRow + (height || 1); r++) {
            for (let c = gridColumn; c < gridColumn + (width || 1); c++) {
              if (grid[r] && c < columnCount) {
                grid[r][c] = true;
              }
            }
          }
        });

        // For title-style text, prefer the top row if possible
        if (isTitleStyle) {
          // Look for space in the top row
          let spaceFound = false;
          for (let c = 0; c <= columnCount - 6; c++) {
            if (!grid[0][c]) {
              spaceFound = true;
              for (let i = 0; i < 6; i++) {
                if (grid[0][c + i]) {
                  spaceFound = false;
                  break;
                }
              }
              if (spaceFound) {
                return { gridColumn: c, gridRow: 0 };
              }
            }
          }
        }

        // For regular text, place at next available row
        return { gridColumn: 0, gridRow: maxRow };
      };

      const { gridColumn, gridRow } = findNextPosition();

      // Create a new text item
      const newText: TextItem = {
        id,
        type: 'text',
        content: isTitleStyle ? 'Dashboard Title' : '',
        placeholder: isTitleStyle ? 'Enter dashboard title...' : 'Enter your text here...',
        gridColumn,
        gridRow,
        width: isTitleStyle ? 12 : 4, // Title is full width, regular text is 4 columns
        height: isTitleStyle ? 1 : 3, // Title is 1 unit tall, regular text is 3 units
        textAlign: isTitleStyle ? 'center' : 'left',
        createdAt: new Date(),
        isNew: true, // Mark as new so it enters edit mode
      };

      // Add the text card to the dashboard
      setOrderedItems(prev => {
        const updatedItems = [...prev, newText];
        // Layout no longer saved to localStorage
        return updatedItems;
      });

      // Notify parent components
      if (onAddItem) {
        setTimeout(() => onAddItem(newText), 0);
      }

      toast.success(isTitleStyle ? 'Title text added' : 'Text card added');
    } catch (error) {
      console.error('Failed to add text:', error);
      toast.error('Failed to add text card');
    }
  };

  const handleAddRichText = () => {
    try {
      // Use a simple timestamp-based ID to avoid potential issues with random numbers
      const id = `text-${Date.now()}`;

      // Find the next suitable position
      const findNextPosition = () => {
        // If no items, start at the top
        if (orderedItems.length === 0) {
          return { gridColumn: 0, gridRow: 0 };
        }

        // Find the maximum row used
        const maxRow = Math.max(...orderedItems.map(item => (item.gridRow || 0) + (item.height || 1)));
        return { gridColumn: 0, gridRow: maxRow };
      };

      const { gridColumn, gridRow } = findNextPosition();

      // Create a new simple text item
      const newText: TextItem = {
        id,
        type: 'text',
        content: '',
        placeholder: 'Enter your text here...',
        gridColumn,
        gridRow,
        width: 6,
        height: 4,
        isRichText: false,
        isNew: true
      };

      // Prevent operations during drag/resize
      if (window.dashboardOperationInProgress ||
          window.isDraggingDashboardItem ||
          window.isResizingDashboardItem) {
        toast.info('Please wait until current operation is complete');
        return;
      }

      // Flag that we're starting an operation
      operationInProgressRef.current = true;

      // First update local state
      setOrderedItems(prevItems => {
        const updatedItems = [...prevItems, newText];
        // Layout no longer saved to localStorage
        return updatedItems;
      });

      // Then notify parent with a delay to avoid update cycles
      setTimeout(() => {
        if (onAddItem) {
          onAddItem(newText);
        }

        // Clear operation flag after a delay
        setTimeout(() => {
          operationInProgressRef.current = false;
        }, 100);

        toast.success('Rich text card added');
      }, 200); // Longer delay to ensure state is settled
    } catch (error) {
      console.error('Error adding rich text:', error);
      operationInProgressRef.current = false;
      toast.error('Failed to add rich text card');
    }
  };

  const handleAutoArrange = () => {
    try {
      if (!orderedItems.length) return;

      // Show a toast to indicate the operation is in progress
      toast.info('Auto-arranging dashboard items...');

      // Sort items by type and size for a more logical arrangement
      // Headings first, then large charts, then medium, then small/text
      const sortedItems = [...orderedItems].sort((a, b) => {
        // Type priority: heading > chart > text
        if (a.type !== b.type) {
          if (a.type === 'heading') return -1;
          if (b.type === 'heading') return 1;
          if (a.type === 'chart' && b.type === 'text') return -1;
          if (a.type === 'text' && b.type === 'chart') return 1;
        }

        // Size priority for charts (larger first)
        if (a.type === 'chart' && b.type === 'chart') {
          const aSize = (a.width || 4) * (a.height || 3);
          const bSize = (b.width || 4) * (b.height || 3);
          return bSize - aSize;
        }

        // Default to current position
        return (a.gridRow - b.gridRow) || (a.gridColumn - b.gridColumn);
      });

      // Initialize grid to track occupied cells
      const occupiedGrid: boolean[][] = [];
      const columnCount = 12; // Standard 12-column grid

      // Function to check if a position is available
      const isPositionAvailable = (col: number, row: number, width: number, height: number) => {
        // Validate boundaries
        if (col < 0 || col + width > columnCount) return false;

        // Check if all required cells are available
        for (let r = row; r < row + height; r++) {
          if (!occupiedGrid[r]) occupiedGrid[r] = Array(columnCount).fill(false);

          for (let c = col; c < col + width; c++) {
            if (occupiedGrid[r][c]) return false;
          }
        }
        return true;
      };

      // Function to mark cells as occupied
      const occupyPosition = (col: number, row: number, width: number, height: number) => {
        for (let r = row; r < row + height; r++) {
          if (!occupiedGrid[r]) occupiedGrid[r] = Array(columnCount).fill(false);

          for (let c = col; c < col + width; c++) {
            occupiedGrid[r][c] = true;
          }
        }
      };

      // Function to find next available position
      const findNextPosition = (width: number, height: number, isHeading: boolean) => {
        // Headings should always start at column 0
        if (isHeading) {
          for (let row = 0; row < 100; row++) {
            if (isPositionAvailable(0, row, columnCount, height)) {
              return { col: 0, row };
            }
          }
        }

        // For other items, try to optimize the layout
        // First try to fill horizontally
        for (let row = 0; row < 100; row++) {
          for (let col = 0; col < columnCount - width + 1; col++) {
            if (isPositionAvailable(col, row, width, height)) {
              return { col, row };
            }
          }
        }

        // If no position found, place at the bottom
        const maxRow = Math.max(...sortedItems.map(item =>
          (item.gridRow || 0) + (item.height || 1)), 0);
        return { col: 0, row: maxRow };
      };

      // Reposition items
      const repositionedItems = sortedItems.map(item => {
        const width = item.width || (item.type === 'chart' ? 4 :
                                    item.type === 'heading' ? 12 : 4);
        const height = item.height || (item.type === 'chart' ? 3 :
                                      item.type === 'heading' ? 1 : 3);
        const isHeading = item.type === 'heading';

        // Find available position
        const { col, row } = findNextPosition(width, height, isHeading);

        // Mark as occupied
        occupyPosition(col, row, width, height);

        // Return updated item
        return {
          ...item,
          gridColumn: col,
          gridRow: row
        };
      });

      // Update ordered items
      setOrderedItems(repositionedItems);

      // Call parent handlers
      if (onReorderItems) {
        setTimeout(() => onReorderItems(repositionedItems), 50);
      } else if (onReorderCharts) {
        const chartItems = repositionedItems
          .filter(item => item.type === 'chart') as SavedChart[];
        setTimeout(() => onReorderCharts(chartItems), 50);
      }

      toast.success('Dashboard layout auto-arranged');
    } catch (error) {
      console.error('Auto-arrange error:', error);
      toast.error('Failed to auto-arrange layout');
    }
  };

  const handleClearAll = () => {
    if (confirm('Are you sure you want to clear all dashboard items? This cannot be undone.')) {
      setOrderedItems([]);

      // No longer need to clear localStorage

      // Notify parent components
      if (onReorderItems) {
        onReorderItems([]);
      } else if (onReorderCharts) {
        onReorderCharts([]);
      }

      toast.success('Dashboard cleared');
    }
  };

  const handleShareDashboard = () => {
    toast.info('Dashboard sharing is not available in offline mode', {
      description: 'This feature requires an internet connection.'
    });
  };

  // Add a more efficient handler for removing items
  const handleRemoveItem = (itemId: string) => {
    try {
      // Update local state immediately for responsive UI
      setOrderedItems(prev => {
        const updatedItems = prev.filter(item => item.id !== itemId);
        // Layout no longer saved to localStorage
        return updatedItems;
      });

      // Notify parent components
      if (onRemoveItem) {
        setTimeout(() => onRemoveItem(itemId), 0);
      } else if (onRemoveChart && orderedItems.find(item => item.id === itemId)?.type === 'chart') {
        setTimeout(() => onRemoveChart && onRemoveChart(itemId), 0);
      }
    } catch (error) {
      console.error('Remove item error:', error);
      toast.error('Failed to remove item');
    }
  };

  // // Final logging on component unmount
  // useEffect(() => {
  //   return () => {
  //     if (orderedItems.length > 0) {
  //       console.log('Dashboard unmounted with:', orderedItems.length, 'items');
  //     }
  //   };
  // }, [orderedItems]);

  // Efficient dashboard rendering with items properly organized
  // Add a cleanup effect to ensure proper cleanup when switching tabs
  useEffect(() => {
    // Reset global state variables on mount - do this immediately
    if (typeof window !== 'undefined') {
      window.saveLayoutTimeout = undefined;
      window.dashboardOperationInProgress = false;
      window.isDraggingDashboardItem = false;
      window.isResizingDashboardItem = false;
      window.activeItemId = undefined;
      window.lastLayoutUpdate = undefined;
    }

    // Remove any stale guidelines - do this immediately
    document.querySelectorAll('.snap-guideline').forEach(el => el.remove());
    document.querySelectorAll('.position-indicator').forEach(el => el.remove());

    // Force a resize event to ensure charts render properly
    window.dispatchEvent(new Event('resize'));

    // Schedule another resize event after a short delay to catch any delayed rendering
    const resizeTimeout = setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);

    return () => {
      // Clean up when component unmounts
      if (typeof window !== 'undefined') {
        // Clear any pending timeouts
        clearTimeout(resizeTimeout);

        if (window.saveLayoutTimeout) {
          clearTimeout(window.saveLayoutTimeout);
          window.saveLayoutTimeout = undefined;
        }

        // Reset global state variables
        window.dashboardOperationInProgress = false;
        window.isDraggingDashboardItem = false;
        window.isResizingDashboardItem = false;
        window.activeItemId = undefined;
        window.lastLayoutUpdate = undefined;
      }

      // Remove any stale guidelines
      document.querySelectorAll('.snap-guideline').forEach(el => el.remove());
      document.querySelectorAll('.position-indicator').forEach(el => el.remove());
    };
  }, []);

  return (
    <div
      className="dashboard-wrapper pb-8 min-h-screen w-full max-w-full"
      ref={dashboardRef}
      data-edit-mode={isEditMode ? 'true' : 'false'}
      style={{
        height: 'auto',
        minHeight: '100vh',
        width: '100vw',
        maxWidth: '100vw',
        overflow: 'hidden'
      }}
    >
      <DashboardToolbar
        isEditMode={isEditMode}
        viewMode={viewMode}
        onToggleEditMode={handleToggleEditMode}
        onExport={handleExport}
        onPrint={handlePrint}
        onAddChart={handleAddChart}
        onAddHeading={handleAddHeading}
        onAddText={() => handleAddText(false)}
        onAddTitle={() => handleAddText(true)}
        onAddRichText={() => handleAddRichText()}
        onSetViewMode={setViewMode}
        onAutoArrange={handleAutoArrange}
        onClearAll={handleClearAll}
        onShareDashboard={handleShareDashboard}
        hasItems={orderedItems.length > 0}
      />

      <div
        className={`dashboard-container min-h-[calc(100vh-100px)] ${viewMode === 'list' ? 'list-view' : ''}`}
        style={{
          height: 'auto',
          minHeight: 'calc(100vh - 100px)',
          width: '100%',
          maxWidth: '100%',
          paddingBottom: '80px',
          paddingLeft: '24px',
          paddingRight: '24px',
          paddingTop: '16px',
          overflow: 'hidden',
          boxSizing: 'border-box'
        }}
      >
        <DashboardLayout
          items={orderedItems}
          onUpdateItem={handleUpdateItem}
          onRemoveItem={handleRemoveItem}
          onSaveLayout={handleSaveLayout}
          isEditMode={isEditMode}
          viewMode={viewMode}
        />

        {orderedItems.length === 0 && (
          <div className="flex flex-col items-center justify-center h-[calc(100vh-180px)] text-muted-foreground">
            <div className="text-lg font-medium mb-2">Your dashboard is empty</div>
            <div className="text-sm max-w-md text-center mb-4">
              Add charts, headings, and text to create your dashboard. You can save charts from the Notebook tab.
            </div>
            <div className="flex gap-2">
              <button
                className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-md text-sm transition-colors"
                onClick={() => handleAddText(true)}
              >
                Add Title
              </button>
              <button
                className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-md text-sm transition-colors"
                onClick={() => handleAddText(false)}
              >
                Add Text
              </button>
              <button
                className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-md text-sm transition-colors"
                onClick={() => handleAddRichText()}
              >
                Add Text Area
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Export the component directly
export { DashboardComponent as Dashboard };
export default DashboardComponent;