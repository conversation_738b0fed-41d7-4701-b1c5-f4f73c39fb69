{"name": "nextjs-clerk-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_OPTIONS=--max_old_space_size=4096 prisma generate && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "postinstall": "prisma generate", "install:langchain": "npm install @langchain/core @langchain/cohere @langchain/google-genai @langchain/community @langchain/pinecone @langchain/openai langchain next-transpile-modules null-loader"}, "dependencies": {"@agentset/ai-sdk": "^1.2.1", "@ai-sdk/cohere": "^1.0.3", "@blocknote/mantine": "^0.17.1", "@blocknote/react": "^0.17.1", "@clerk/nextjs": "^5.1.5", "@codesandbox/sandpack-react": "^2.19.10", "@codesandbox/sandpack-themes": "^2.0.21", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^8.0.0", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.2", "@google/generative-ai": "^0.21.0", "@hello-pangea/dnd": "^18.0.1", "@kanaries/graphic-walker": "^0.4.74", "@langchain/cohere": "^0.3.3", "@langchain/community": "^0.3.22", "@langchain/core": "^0.2.36", "@langchain/openai": "^0.3.14", "@langchain/pinecone": "^0.2.0", "@monaco-editor/react": "^4.6.0", "@nivo/bump": "^0.87.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@nivo/sankey": "^0.88.0", "@nivo/tree": "^0.87.0", "@nivo/treemap": "^0.87.0", "@pdf-viewer/react": "^1.5.1", "@pinecone-database/pinecone": "^5.1.2", "@prisma/client": "^5.19.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.8", "@react-hook/intersection-observer": "^3.1.2", "@react-hook/passive-layout-effect": "^1.2.1", "@react-pdf/renderer": "^3.4.4", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.12.0", "@types/qrcode": "^1.5.5", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.13", "@types/react-syntax-highlighter": "^15.5.11", "@types/sql.js": "^1.4.9", "@uploadthing/react": "^7.1.5", "@y-sweet/react": "^0.8.2", "agentset": "^1.3.0", "ai": "^4.0.0", "alasql": "^3.1.0", "antd": "^5.24.1", "autoprefixer": "^10.4.16", "canvas-confetti": "^1.9.3", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cohere-ai": "^7.7.7", "danfojs": "^1.1.2", "date-fns": "^3.6.0", "dedent": "^1.5.3", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^10.16.4", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "langchain": "^0.2.20", "lodash": "^4.17.21", "lucide-react": "^0.440.0", "mammoth": "^1.8.0", "mathjs": "^13.1.1", "mermaid": "^11.4.1", "mini-svg-data-uri": "^1.4.4", "mongodb": "^6.10.0", "nanoid": "^4.0.2", "next": "^14.2.14", "next-themes": "^0.3.0", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.2.133", "postcss": "^8.4.33", "prism-react-renderer": "^2.3.1", "pusher": "^5.2.0", "pusher-js": "^8.3.0", "python-shell": "^5.0.0", "qrcode": "^1.5.4", "re-resizable": "^6.9.9", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-error-boundary": "^4.0.13", "react-grid-layout": "^1.5.1", "react-heatmap-grid": "^0.9.1", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-modal": "^3.16.1", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.4", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.10", "reactflow": "^11.11.4", "recharts": "^2.14.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.5.0", "uuid": "^11.0.3", "vaul": "^0.9.2", "xlsx": "^0.18.5", "y-webrtc": "^10.3.0", "yjs": "^13.6.24", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.4", "@types/pdfjs-dist": "^2.10.378", "@types/react": "^18.2.36", "@types/react-dom": "^18", "@types/react-grid-layout": "^1.3.5", "@types/react-resizable": "^3.0.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prisma": "^5.19.1", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.3.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5"}}