import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, RefreshCw, Upload, MessageSquare, FileUp, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// Define PDF document type
interface PDFDocument {
  id: string;
  fileName: string;
  fileSize: number;
  createdAt: string | Date;
  embedding?: boolean;
  embeddingModel?: string;
}

interface PDFSelectorProps {
  selectedPDF: string | null;
  onSelectPDF: (pdfId: string) => void;
  onEmbedPDF: (pdfId: string, file: File) => Promise<boolean>;
  onDeleteEmbedding?: (pdfId: string) => Promise<boolean>;
  isEmbedding: boolean;
}

const PDFSelector: React.FC<PDFSelectorProps> = ({
  selectedPDF,
  onSelectPDF,
  onEmbedPDF,
  onDeleteEmbedding,
  isEmbedding
}) => {
  const [pdfs, setPDFs] = useState<PDFDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  // Fetch PDFs directly from the API
  const fetchPDFs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/pdf-documents', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch PDFs: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.documents) {
        console.log('Fetched PDFs:', data.documents);
        setPDFs(data.documents);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch PDFs');
      console.error('Error fetching PDFs:', err);
      toast.error(`Error fetching PDFs: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch PDFs on component mount
  useEffect(() => {
    fetchPDFs();
  }, []);

  const handleEmbedClick = async () => {
    if (selectedPDF && selectedFile) {
      const success = await onEmbedPDF(selectedPDF, selectedFile);
      if (success) {
        // Refresh PDFs to update embedding status
        fetchPDFs();
      }
    } else {
      toast.error('Please select both a PDF and upload the file');
    }
  };

  const handleDeleteEmbeddingClick = async () => {
    if (selectedPDF && onDeleteEmbedding) {
      if (window.confirm('Are you sure you want to delete the embeddings for this PDF? This action cannot be undone.')) {
        const success = await onDeleteEmbedding(selectedPDF);
        if (success) {
          // Refresh PDFs to update embedding status
          fetchPDFs();
          toast.success('PDF embeddings deleted successfully');
        }
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.type !== 'application/pdf') {
        toast.error('Please select a PDF file');
        return;
      }
      setSelectedFile(file);
    }
  };

  // Find the selected PDF object
  const selectedPDFObj = pdfs.find(d => d.id === selectedPDF);

  // Count embedded PDFs
  const embeddedPDFsCount = pdfs.filter(d => d.embedding === true).length;

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-medium">PDF Selection</h3>
        </div>
        <Badge variant={embeddedPDFsCount > 0 ? "success" : "outline"}>
          {embeddedPDFsCount} Embedded
        </Badge>
      </div>

      <div className="flex flex-wrap items-center gap-2">
        <Select
          value={selectedPDF || ''}
          onValueChange={onSelectPDF}
          disabled={isLoading || isEmbedding}
        >
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Select a PDF" />
          </SelectTrigger>
          <SelectContent>
            {pdfs.length === 0 ? (
              <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                No PDFs available. Please upload a PDF first.
              </div>
            ) : (
              <>
                {/* Show embedded PDFs first */}
                {pdfs.filter(d => d.embedding === true).length > 0 && (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground font-medium">
                    Embedded PDFs
                  </div>
                )}
                {pdfs.filter(d => d.embedding === true).map((pdf) => (
                  <SelectItem key={pdf.id} value={pdf.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{pdf.fileName}</span>
                      <Badge variant="outline" className="ml-2 bg-green-100 text-green-800">
                        Embedded
                      </Badge>
                    </div>
                  </SelectItem>
                ))}

                {/* Then show non-embedded PDFs */}
                {pdfs.filter(d => d.embedding !== true).length > 0 && (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground font-medium mt-1">
                    Other PDFs
                  </div>
                )}
                {pdfs.filter(d => d.embedding !== true).map((pdf) => (
                  <SelectItem key={pdf.id} value={pdf.id}>
                    <span>{pdf.fileName}</span>
                  </SelectItem>
                ))}
              </>
            )}
          </SelectContent>
        </Select>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                onClick={fetchPDFs}
                disabled={isLoading || isEmbedding}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Refresh PDFs</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <FileUp className="h-4 w-4 mr-2" />
              Upload PDF
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload PDF</DialogTitle>
              <DialogDescription>
                Upload a PDF file to embed in the vector database.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="pdf-file">PDF File</Label>
                <Input
                  id="pdf-file"
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                onClick={() => {
                  if (selectedFile) {
                    // TODO: Implement PDF upload
                    toast.success(`File ${selectedFile.name} selected`);
                    setUploadDialogOpen(false);
                  } else {
                    toast.error('Please select a file');
                  }
                }}
              >
                Upload
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {selectedPDFObj && selectedPDFObj.embedding !== true && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleEmbedClick}
                  disabled={isEmbedding || isLoading || !selectedFile}
                  className="ml-2"
                >
                  {isEmbedding ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Embedding...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Embed PDF
                    </>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Embed the selected PDF in the vector database</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {selectedPDFObj && selectedPDFObj.embedding === true && (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-2"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Ready to Chat
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>This PDF is already embedded and ready for chat</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {onDeleteEmbedding && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="ml-2"
                      onClick={handleDeleteEmbeddingClick}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Embeddings
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Delete the embeddings for this PDF from Pinecone</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </>
        )}
      </div>

      {selectedPDFObj && (
        <div className="text-sm text-muted-foreground border p-3 rounded-md">
          <p>
            <span className="font-medium">Selected:</span> {selectedPDFObj.fileName}
          </p>
          <p>
            <span className="font-medium">Size:</span> {Math.round(selectedPDFObj.fileSize / 1024)} KB
            <span className="font-medium ml-3">Created:</span> {new Date(selectedPDFObj.createdAt).toLocaleDateString()}
          </p>
          <p>
            <span className="font-medium">Status:</span>
            {selectedPDFObj.embedding === true ? (
              <span className="text-green-600 font-medium"> Embedded with {selectedPDFObj.embeddingModel || 'Cohere'}</span>
            ) : (
              <span className="text-amber-600"> Not embedded yet</span>
            )}
          </p>
        </div>
      )}

      {error && (
        <div className="text-sm text-red-500 p-2 border border-red-200 rounded-md bg-red-50">
          {error}
        </div>
      )}
    </div>
  );
};

export default PDFSelector;
