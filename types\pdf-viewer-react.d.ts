declare module '@pdf-viewer/react' {
  import { ReactNode, CSSProperties } from 'react';

  interface RPProviderProps {
    src: string;
    children: ReactNode;
    withCredentials?: boolean;
    onDocumentLoad?: () => void;
    onError?: (error: any) => void;
    initialPage?: number;
    defaultScale?: number;
  }

  interface RPDefaultLayoutProps {
    children: ReactNode;
    style?: CSSProperties;
  }

  interface RPPagesProps {
    // Add any props for RPPages if needed
  }

  interface RPConfigProps {
    children: ReactNode;
  }

  export const RPProvider: React.FC<RPProviderProps>;
  export const RPDefaultLayout: React.FC<RPDefaultLayoutProps>;
  export const RPPages: React.FC<RPPagesProps>;
  export const RPConfig: React.FC<RPConfigProps>;
}
