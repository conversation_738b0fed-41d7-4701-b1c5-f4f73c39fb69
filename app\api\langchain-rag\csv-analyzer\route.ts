import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { <PERSON>t<PERSON><PERSON>ere } from '@langchain/cohere';
import { ChatTogetherAI } from '@langchain/community/chat_models/togetherai';
import {
  ChatPromptTemplate,
  HumanMessagePromptTemplate,
  SystemMessagePromptTemplate,
} from '@langchain/core/prompts';
import { getModelProvider, getModelId } from '../chat/models';
import prisma from '@/lib/db';
import { PythonShell } from 'python-shell';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

// Initialize Pinecone client
const getPineconeClient = () => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Get embeddings model
const getEmbeddings = () => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_query', // For query embeddings
    // @ts-ignore - truncate is a valid parameter but not in the type definitions
    truncate: 'END', // Truncate long texts from the end
    embeddingFormat: 'float' // Use float format for better precision
  });
};

// Get LLM based on selected model
const getLLM = (model: string) => {
  const provider = getModelProvider(model);
  const modelId = getModelId(model, provider);

  if (provider === 'cohere') {
    const apiKey = process.env.COHERE_API_KEY;
    if (!apiKey) {
      throw new Error('COHERE_API_KEY is not defined in environment variables');
    }

    return new ChatCohere({
      apiKey,
      model: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000, // Increase output token limit
    });
  } else {
    const apiKey = process.env.TOGETHER_API_KEY;
    if (!apiKey) {
      throw new Error('TOGETHER_API_KEY is not defined in environment variables');
    }

    return new ChatTogetherAI({
      apiKey,
      modelName: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000, // Increase output token limit
    });
  }
};

// Execute Python code safely
const executePythonCode = async (code: string, csvData: string): Promise<string> => {
  // Create a temporary directory
  const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'csv-analyzer-'));

  // Save CSV data to a file
  const csvFilePath = path.join(tempDir, 'data.csv');
  fs.writeFileSync(csvFilePath, csvData);

  // Create a Python script with the code
  const scriptPath = path.join(tempDir, 'script.py');

  // Prepare the Python script with safety measures
  const safeCode = `
import pandas as pd
import numpy as np
import sys
import json
from io import StringIO

# Limit execution time and resource usage
import resource
import signal

# Set resource limits
resource.setrlimit(resource.RLIMIT_CPU, (30, 30))  # 30 seconds CPU time
resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))  # 1GB memory

# Set timeout handler
def timeout_handler(signum, frame):
    print(json.dumps({"error": "Execution timed out"}))
    sys.exit(1)

signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(30)  # 30 seconds timeout

# Load the CSV data
try:
    df = pd.read_csv("${csvFilePath.replace(/\\/g, '\\\\')}")

    # Execute the user code in a safe context
    result = None
    def get_result():
${code.split('\n').map(line => '        ' + line).join('\n')}
        return result

    result = get_result()

    # Format the result for output
    if isinstance(result, pd.DataFrame):
        if len(result) > 100:
            result = result.head(100)
        output = result.to_string()
    elif isinstance(result, np.ndarray):
        output = str(result)
    else:
        output = str(result)

    print(json.dumps({"result": output}))
except Exception as e:
    print(json.dumps({"error": str(e)}))
finally:
    # Clean up
    signal.alarm(0)
`;

  fs.writeFileSync(scriptPath, safeCode);

  try {
    // Execute the Python script
    const options = {
      mode: 'text',
      pythonPath: 'python', // Use system Python
      scriptPath: tempDir,
    };

    const results = await PythonShell.run('script.py', options);
    const result = results.join('\n');

    // Parse the JSON result
    try {
      const jsonResult = JSON.parse(result);
      if (jsonResult.error) {
        return `Error: ${jsonResult.error}`;
      }
      return jsonResult.result || 'No result returned';
    } catch (e) {
      return `Error parsing result: ${result}`;
    }
  } catch (error: any) {
    return `Execution error: ${error.message}`;
  } finally {
    // Clean up temporary files
    try {
      fs.unlinkSync(scriptPath);
      fs.unlinkSync(csvFilePath);
      fs.rmdirSync(tempDir);
    } catch (e) {
      console.error('Error cleaning up temporary files:', e);
    }
  }
};

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const {
      message,
      datasetId,
      model = 'command-r-plus',
    } = body;

    if (!message || !datasetId) {
      return NextResponse.json({ error: 'Message and datasetId are required' }, { status: 400 });
    }

    // Get user ID from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get dataset from database
    const dataset = await prisma.dataSet.findUnique({
      where: { id: datasetId },
      select: { id: true, name: true, data: true, userId: true }
    });

    if (!dataset) {
      return NextResponse.json({ error: 'Dataset not found' }, { status: 404 });
    }

    if (dataset.userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to dataset' }, { status: 403 });
    }

    // Ensure dataset.data is a string
    const datasetContent = typeof dataset.data === 'string'
      ? dataset.data
      : JSON.stringify(dataset.data);

    // Get LLM
    const llm = getLLM(model);

    // Create prompt for code generation
    const codeGenPrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(
        `You are a Python data analysis expert. Your task is to generate Python code to analyze CSV data based on the user's question.
        The code should:
        1. Use pandas to analyze the CSV data
        2. Be efficient and handle large datasets
        3. Return the specific answer to the user's question
        4. Store the final result in a variable called 'result'
        5. Include comments explaining the approach
        6. Handle potential errors gracefully

        DO NOT include any imports, file reading, or print statements. The CSV is already loaded as 'df'.
        ONLY return the Python code without any explanations or markdown formatting.`
      ),
      HumanMessagePromptTemplate.fromTemplate(
        `I have a CSV dataset with the following structure:

        First few rows:
        ${datasetContent.substring(0, 1000)}

        User question: ${message}

        Generate Python code to answer this question using the dataset.`
      ),
    ]);

    // Generate Python code
    console.log('Generating Python code for CSV analysis...');
    const codeResponse = await llm.invoke(await codeGenPrompt.format({}));
    const pythonCode = typeof codeResponse.content === 'string'
      ? codeResponse.content.trim()
      : JSON.stringify(codeResponse.content);

    // Clean up the code (remove markdown formatting if present)
    const cleanCode = pythonCode
      .replace(/```python/g, '')
      .replace(/```/g, '')
      .trim();

    console.log('Generated Python code:', cleanCode);

    // Execute the Python code
    console.log('Executing Python code...');
    const executionResult = await executePythonCode(cleanCode, datasetContent);
    console.log('Execution result:', executionResult);

    // Create prompt for final answer generation
    const finalAnswerPrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(
        `You are an AI assistant that helps users analyze CSV data.
        You've just executed Python code to analyze a dataset based on the user's question.
        Provide a clear, concise answer based on the execution results.
        If there was an error, explain what might have gone wrong and suggest alternatives.
        Include relevant numbers and insights from the data.
        Format the response in a readable way, using markdown for tables if appropriate.`
      ),
      HumanMessagePromptTemplate.fromTemplate(
        `User question: ${message}

        Python code executed:
        \`\`\`python
        ${cleanCode}
        \`\`\`

        Execution result:
        ${executionResult}

        Please provide a clear answer to the user's question based on these results.`
      ),
    ]);

    // Generate final answer
    console.log('Generating final answer...');
    const finalResponse = await llm.invoke(await finalAnswerPrompt.format({}));
    const finalAnswer = typeof finalResponse.content === 'string'
      ? finalResponse.content
      : JSON.stringify(finalResponse.content);

    // Return the response
    return NextResponse.json({
      content: finalAnswer,
      model: model,
      provider: getModelProvider(model),
      codeExecuted: cleanCode,
      executionResult: executionResult,
      datasetName: dataset.name
    });
  } catch (error: any) {
    console.error('Error in CSV analyzer API:', error);
    return NextResponse.json({
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
