These are new fields need to be added in the action.ts and in the invoice page
date 2024-10-17

estLocataire      Boolean? // Indicates if the employee is renting
possedeAppartement Boolean? // Indicates if the employee owns an apartment
utiliseTransport  Boolean? // Indicates if the employee uses transport
typeTransport     String?  // Type of transport used (e.g., "PUBLIC", "PRIVATE", "COMPANY")
distanceDomicileTravail Float? // Distance between home and work in kilometers
numeroCIMR        String? // Caisse Interprofessionnelle Marocaine de Retraite number
groupeSanguin     String? // Blood type
situationFamiliale String? // Detailed family situation
zoneResidence     String? // Residential area or neighborhood
modePaiement      String? // Payment method (e.g., "BANK_TRANSFER", "CHEQUE")
echelonSalaire    String? // Salary grade or level
formationsContinues String[] // Ongoing or completed professional training

date 2024-10-21
- add the cover image to the editor
- add the title to the editor
- add the upload image button to the editor
- add the RIB number the invoice page [In progress] 

this the format should ai generate to visualize data: 
{
  "title": "Sample Data",
  "description": "Sample description",
  "data": [
    { "name": "Category A", "value": 30, "growth": 5 },
    { "name": "Category B", "value": 45, "growth": 8 },
    { "name": "Category C", "value": 60, "growth": 12 }
  ],
  "columns": [
    { "key": "name", "label": "Category" },
    { "key": "value", "label": "Value" },
    { "key": "growth", "label": "Growth %" }
  ],
  "insight": "Growing trend observed across categories",
  "footer": "Data from Q1 2024"
}

- Remoove chartconfig only keep chartvisulizer component [QueryResult] {DONE}
- implment a json output of chart config selection (x and y, title , color, chart type and all of them), and remove duplicate
also make the the config above inside a dropdown to hide it, so it will show only chart
[ChartVisualizer]

- Update hr/layout by adding screen animation when sidebar exapnd
- update app-sidebar by adding mainsidebar functionality
- update workspace navbar, and management minisidebar + testing UX 


github pass
<EMAIL>
BusinessBlock5@


please make sure the big data like csv can be retrieve by using deep research technique please search for deep research in web and how it works then implmented it, also make sure the reference also when i click it it show it in a tooltip or when i hover over it (choose what suit for it), also please make sure i can delete an embeded file from pincone please if user want 