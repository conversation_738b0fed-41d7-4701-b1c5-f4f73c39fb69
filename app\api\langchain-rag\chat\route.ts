import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { <PERSON>t<PERSON><PERSON><PERSON> } from '@langchain/cohere';
import { ChatTogetherAI } from '@langchain/community/chat_models/togetherai';
// We're using our own document formatter
import {
  ChatPromptTemplate,
  HumanMessagePromptTemplate,
  SystemMessagePromptTemplate,
} from '@langchain/core/prompts';

// Import model mappings from separate file
import { getModelProvider, getModelId } from './models';

// Initialize Pinecone client
const getPineconeClient = () => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    api<PERSON><PERSON>,
  });
};

// Initialize Cohere embeddings with settings to match llama-text-embed-v2
const getEmbeddings = () => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_query', // For query embeddings
    // @ts-ignore - truncate is a valid parameter but not in the type definitions
    truncate: 'END', // Truncate long texts from the end
    embeddingFormat: 'float' // Use float format for better precision
  });
};

// Get LLM based on selected model
const getLLM = (model: string) => {
  const provider = getModelProvider(model);
  const modelId = getModelId(model, provider);

  if (provider === 'cohere') {
    const apiKey = process.env.COHERE_API_KEY;
    if (!apiKey) {
      throw new Error('COHERE_API_KEY is not defined in environment variables');
    }

    return new ChatCohere({
      apiKey,
      model: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000, // Increase output token limit
    });
  } else {
    const apiKey = process.env.TOGETHER_API_KEY;
    if (!apiKey) {
      throw new Error('TOGETHER_API_KEY is not defined in environment variables');
    }

    return new ChatTogetherAI({
      apiKey,
      modelName: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000, // Increase output token limit
    });
  }
};

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const {
      messages,
      datasetId,
      pdfId,
      model = 'command-r-plus',
      namespace = 'adeloop', // Use adeloop namespace directly
      useNamespaceOnly = false
    } = body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Messages are required' }, { status: 400 });
    }

    // Get the last user message
    const lastUserMessage = messages[messages.length - 1];
    if (lastUserMessage.role !== 'user') {
      return NextResponse.json({ error: 'Last message must be from user' }, { status: 400 });
    }

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = process.env.PINECONE_INDEX || 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Create vector store
    const vectorStore = await PineconeStore.fromExistingIndex(embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
    });

    // Create retriever with proper configuration
    // Ensure filter matches the exact metadata structure used during embedding
    let metadataFilter = undefined;

    if (!useNamespaceOnly) {
      if (datasetId) {
        metadataFilter = { source: 'dataset', datasetId: datasetId };
        console.log('Using dataset filter:', metadataFilter);
      } else if (pdfId) {
        metadataFilter = { source: 'pdf', pdfId: pdfId };
        console.log('Using PDF filter:', metadataFilter);
      }
    }

    const retriever = vectorStore.asRetriever({
      searchType: "similarity",
      k: 8, // Increase number of documents to retrieve for better context
      filter: metadataFilter
    });

    // Log the retriever configuration with more details
    console.log('Retriever configuration:', {
      searchType: "similarity",
      k: 8,
      filter: metadataFilter,
      namespace,
      datasetId,
      pdfId,
      useNamespaceOnly
    });

    // Log the Pinecone query parameters for debugging
    console.log('Pinecone query parameters:', {
      namespace: namespace,
      topK: 8,
      filter: metadataFilter
    });

    // Get LLM
    const llm = getLLM(model);

    // Create prompt template
    const systemTemplate =
      `You are an AI assistant that answers questions based on the provided context.
      Use ONLY the information from the context to answer the question.
      If you don't know the answer based on the context, say "I don't have enough information in the provided data to answer this question."
      Always include relevant details from the context in your answer.
      Be specific and reference the source of information in your answer (e.g., "According to the dataset..." or "As mentioned in the PDF...").

      The context contains information from datasets and/or PDF documents that the user has selected.
      Each piece of context is labeled with its source.

      IMPORTANT: You must ONLY use the information in the context below. Do NOT use any prior knowledge.
      If the context doesn't contain relevant information, admit that you don't have enough information.

      Context:
      {context}`;

    const humanTemplate = '{question}';

    const chatPrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(systemTemplate),
      HumanMessagePromptTemplate.fromTemplate(humanTemplate),
    ]);

    // Retrieve documents first - using the invoke method to avoid deprecation warning
    console.log('Retrieving documents for query:', lastUserMessage.content);

    let docs;
    try {
      docs = await retriever.invoke(lastUserMessage.content);
      console.log(`Retrieved ${docs.length} documents from vector store`);

      // Log the first few documents for debugging
      if (docs.length > 0) {
        console.log('Sample documents:',
          docs.slice(0, 2).map(doc => ({
            metadata: doc.metadata,
            contentPreview: doc.pageContent.substring(0, 100) + '...'
          }))
        );
      }
    } catch (error) {
      const retrievalError = error as Error;
      console.error('Error retrieving documents:', retrievalError);
      return NextResponse.json({
        content: "I encountered an error while searching for relevant information. Please try again or contact support if the issue persists.",
        model: model,
        provider: getModelProvider(model),
        sourceDocuments: [],
        error: retrievalError.message || 'Unknown error during retrieval'
      });
    }

    // Process documents to ensure they are strings
    const processedDocs = docs.map(doc => {
      if (typeof doc.pageContent !== 'string') {
        doc.pageContent = JSON.stringify(doc.pageContent);
      }
      return doc;
    });

    // Check if we have any documents
    if (processedDocs.length === 0) {
      console.log('No relevant documents found for query:', lastUserMessage.content);

      // Provide more specific feedback based on the selected source
      let noDocsMessage = "I couldn't find any relevant information in the selected data sources to answer your question. ";

      if (datasetId) {
        noDocsMessage += "The selected dataset might not contain information related to your question. ";
      } else if (pdfId) {
        noDocsMessage += "The selected PDF might not contain information related to your question. ";
      }

      noDocsMessage += "Please try a different question or check if your data contains the information you're looking for.";

      return NextResponse.json({
        content: noDocsMessage,
        model: model,
        provider: getModelProvider(model),
        sourceDocuments: []
      });
    }

    // Format documents as a string with detailed source information
    const formattedDocsText = processedDocs.map((doc, i) => {
      const source = doc.metadata?.source || 'unknown';
      let sourceInfo = '';

      if (source === 'pdf') {
        sourceInfo = `PDF: ${doc.metadata?.fileName || 'Unknown'} (Page ${doc.metadata?.page || 'N/A'})`;
      } else if (source === 'dataset') {
        sourceInfo = `Dataset: ${doc.metadata?.datasetName || 'Unknown'} (Row ${doc.metadata?.rowIndex !== undefined ? doc.metadata.rowIndex + 1 : 'N/A'})`;
      } else {
        sourceInfo = `Source: ${source}`;
      }

      // Add metadata debug info
      console.log(`Document ${i+1} metadata:`, doc.metadata);

      return `Document ${i+1} [${sourceInfo}]: ${doc.pageContent}`;
    }).join('\n\n');

    // Log the formatted context for debugging
    console.log('Formatted context length:', formattedDocsText.length);
    console.log('Formatted context preview:', formattedDocsText.substring(0, 200) + '...');

    // Log the documents being used
    console.log(`Using ${processedDocs.length} documents for query:`,
      processedDocs.map(doc => ({
        source: doc.metadata?.source,
        datasetName: doc.metadata?.datasetName,
        fileName: doc.metadata?.fileName,
        contentPreview: doc.pageContent.substring(0, 50) + '...'
      }))
    );

    // Store the documents for reference in the response
    const sourceDocuments = processedDocs.map((doc, i) => ({
      content: doc.pageContent,
      metadata: doc.metadata,
      index: i + 1
    }));

    // Create a simpler chain that doesn't use pipe operations
    const prompt = await chatPrompt.format({
      context: formattedDocsText,
      question: lastUserMessage.content
    });

    // Execute LLM directly with the formatted prompt
    console.log('Sending prompt to LLM:', {
      model: model,
      provider: getModelProvider(model),
      promptLength: prompt.length,
      promptPreview: prompt.substring(0, 200) + '...'
    });

    const llmResponse = await llm.invoke(prompt);

    console.log('Raw LLM response:', JSON.stringify(llmResponse, null, 2));

    // Extract the text content in a type-safe way
    let response = '';

    try {
      // Use a safer approach to extract text from the response
      const extractText = async () => {
        // Try to extract content directly from AIMessage
        try {
          // For AIMessage objects, try to get content directly
          if (llmResponse && typeof llmResponse === 'object' && 'content' in llmResponse) {
            const content = llmResponse.content;
            if (typeof content === 'string' && content.trim() !== '') {
              return content;
            }
          }
        } catch (e) {
          console.log('Direct content extraction failed:', e);
        }

        // If that fails, try to extract text based on common response formats
        const responseObj = llmResponse as any; // Use any type for flexible property access

        // Try different properties where text content might be found
        if (responseObj) {
          // Check for direct content
          if (responseObj.content && typeof responseObj.content === 'string') {
            return responseObj.content;
          }

          // Check for text property (used in some models)
          if (responseObj.text && typeof responseObj.text === 'string') {
            return responseObj.text;
          }

          // Check for message.content pattern
          if (responseObj.message && typeof responseObj.message === 'object') {
            if (responseObj.message.content && typeof responseObj.message.content === 'string') {
              return responseObj.message.content;
            }
          }

          // Check for choices array (OpenAI-like format)
          if (responseObj.choices && Array.isArray(responseObj.choices) && responseObj.choices.length > 0) {
            const choice = responseObj.choices[0];
            if (choice) {
              if (choice.text && typeof choice.text === 'string') {
                return choice.text;
              }
              if (choice.message && typeof choice.message === 'object' &&
                  choice.message.content && typeof choice.message.content === 'string') {
                return choice.message.content;
              }
            }
          }

          // Check for generations array (LangChain format)
          if (responseObj.generations && Array.isArray(responseObj.generations) && responseObj.generations.length > 0) {
            const generation = responseObj.generations[0];
            if (generation) {
              if (generation.text && typeof generation.text === 'string') {
                return generation.text;
              }
              if (generation.message && typeof generation.message === 'object' &&
                  generation.message.content && typeof generation.message.content === 'string') {
                return generation.message.content;
              }
            }
          }
        }

        // If we can't find text in a structured way, convert the whole object to string
        return JSON.stringify(responseObj);
      };

      // Get the text content
      response = await extractText();
    } catch (parseError) {
      console.error('Error parsing LLM response:', parseError);
      // Fallback to string conversion of the whole object
      response = typeof llmResponse === 'string'
        ? llmResponse
        : JSON.stringify(llmResponse);
    }

    // Ensure we have a non-empty response
    if (!response || response.trim() === '' || response === '{}') {
      response = "I couldn't generate a proper response. Please try again with a different question.";
    }

    return NextResponse.json({
      content: response,
      model: model,
      provider: getModelProvider(model),
      sourceDocuments: sourceDocuments
    });
  } catch (error: any) {
    console.error('Error in LangChain RAG chat API:', error);
    return NextResponse.json({
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
