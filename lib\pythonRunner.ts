import fetch from 'cross-fetch';

export class PythonRunner {
  private static instance: PythonRunner;
  private baseUrl: string;
  private authToken: string | null;
  private tokenExpiry: number | null;
  private isOfflineMode: boolean = false;

  private constructor() {
    this.baseUrl = 'http://127.0.0.1:8000';
    this.authToken = null;
    this.tokenExpiry = null;
  }

  static getInstance(): PythonRunner {
    if (!PythonRunner.instance) {
      PythonRunner.instance = new PythonRunner();
    }
    return PythonRunner.instance;
  }

  setAuthToken(token: string) {
    this.authToken = token;
    this.tokenExpiry = Date.now() + 3600000; // 1 hour expiry
    console.log('PythonRunner token set:', token.substring(0, 20) + '...');
  }

  setOfflineMode(enabled: boolean) {
    this.isOfflineMode = enabled;
  }

  private isTokenValid(): boolean {
    return !!(this.authToken && this.tokenExpiry && Date.now() < this.tokenExpiry);
  }

  private formatPythonError(error: string): string {
    // Format Python error messages to be more readable
    const lines = error.split('\n');
    if (lines.length > 1) {
      return lines[lines.length - 1]; // Return the last line which usually contains the actual error
    }
    return error;
  }

  async execute(code: string, datasetId: string) {
    try {
      if (this.isOfflineMode) {
        return this.executeOffline(code, datasetId);
      }

      if (!this.isTokenValid()) {
        throw new Error('Auth token is invalid or expired');
      }

      // Add common imports and utilities if not present in the code
      const enhancedCode = this.enhanceCode(code);

      const response = await fetch(`${this.baseUrl}/api/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.authToken!,
        },
        body: JSON.stringify({
          query: enhancedCode,
          language: 'python',
          datasetId,
        }),
      });

      if (response.status === 401) {
        throw new Error('Authentication failed');
      }

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Python execution failed:', {
          status: response.status,
          data: errorData,
          datasetId
        });
        throw new Error(this.formatPythonError(errorData.detail || `HTTP error! status: ${response.status}`));
      }

      const result = await response.json();

      // Handle plots if present
      if (result.plots?.length > 0) {
        result.plots = result.plots.map((plot: string) => {
          // Check if the plot is already a data URL
          if (plot.startsWith('data:image')) {
            return plot;
          }
          return `data:image/png;base64,${plot}`;
        });
        console.log(`Processed ${result.plots.length} plots from Python execution`);
      }

      // Format DataFrame results
      if (result.data && Array.isArray(result.data)) {
        // Keep only the first 1000 rows for performance
        result.data = result.data.slice(0, 1000);
      }

      console.log('Python execution succeeded with result shape:',
        result.data ? result.data.length : 'no data');
      return result;

    } catch (error) {
      if (!this.isOfflineMode) {
        // If online execution fails, try offline mode
        this.setOfflineMode(true);
        return this.executeOffline(code, datasetId);
      }
      throw error;
    }
  }

  private enhanceCode(code: string): string {
    // Add common imports if not present
    const imports = [
      'import pandas as pd',
      'import numpy as np',
      'import matplotlib.pyplot as plt',
      'import seaborn as sns'
    ];

    // Add helpful plot setup code if not present
    const plotSetup = [
      '# Set plot style for better visualization',
      'plt.style.use("default")',
      'sns.set_theme(style="whitegrid")',
      'plt.rcParams.update({"figure.figsize": (10, 6)})',
    ];

    // Check if code already has imports
    const hasImports = imports.some(imp => code.includes(imp));
    // Check if code already has plot setup
    const hasPlotSetup = code.includes('plt.style.use') || code.includes('sns.set_theme');

    let enhancedCode = code;

    // Add imports if needed
    if (!hasImports) {
      enhancedCode = `${imports.join('\n')}\n\n${enhancedCode}`;
    }

    // Add plot setup if needed and if code seems to be creating plots
    if (!hasPlotSetup && (code.includes('plt.') || code.includes('sns.'))) {
      enhancedCode = `${plotSetup.join('\n')}\n\n${enhancedCode}`;
    }

    // Add a reminder about get_plot() if the code creates plots but doesn't call get_plot()
    if ((code.includes('plt.') || code.includes('sns.')) && !code.includes('get_plot()')) {
      enhancedCode += '\n\n# Uncomment the next line to return the plot as an image\n# result = get_plot()'
    }

    return enhancedCode;
  }

  private async executeOffline(code: string, datasetId: string) {
    const { getMockDataset } = await import('@/utils/mockData');
    const dataset = getMockDataset(datasetId);
    
    if (!dataset) {
      throw new Error('Dataset not found');
    }

    // Add common imports and utilities if not present
    const enhancedCode = this.enhanceCode(code);

    // Simulate Python execution with mock data
    let result: any = {
      data: dataset.data,
      output: `Running in offline mode\nDataset shape: (${dataset.data.length}, ${Object.keys(dataset.data[0]).length})\n`,
      plots: []
    };

    // Handle basic DataFrame operations
    if (code.includes('df.head()')) {
      result.data = dataset.data.slice(0, 5);
    } else if (code.includes('df.describe()')) {
      result.data = this.generateDescriptiveStats(dataset.data);
    }

    // Handle plot generation
    if (code.includes('get_plot()')) {
      result.plots = [this.generateMockPlot(code, dataset.data)];
    }

    return result;
  }

  private generateDescriptiveStats(data: any[]) {
    // Generate basic statistical summary
    const numericColumns = Object.keys(data[0]).filter(key => 
      typeof data[0][key] === 'number'
    );

    return numericColumns.map(col => {
      const values = data.map(row => row[col]).filter(v => !isNaN(v));
      return {
        column: col,
        count: values.length,
        mean: values.reduce((a, b) => a + b, 0) / values.length,
        std: Math.sqrt(values.reduce((a, b) => a + Math.pow(b - (values.reduce((c, d) => c + d, 0) / values.length), 2), 0) / values.length),
        min: Math.min(...values),
        max: Math.max(...values)
      };
    });
  }

  private generateMockPlot(code: string, data: any[]): string {
    // Base64 encoded simple plot placeholder
    // You can enhance this to generate different types of mock plots based on the code
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'; // Add your base64 mock plot image
  }
}

