import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { Document } from '@langchain/core/documents';
import { PDFLoader } from "langchain/document_loaders/fs/pdf";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import prisma from '@/lib/db';

// Initialize Pinecone client
const getPineconeClient = () => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings
const getEmbeddings = () => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_document', // For document embeddings
    truncate: 'END', // Truncate long texts from the end
    embeddingFormat: 'float' // Use float format for better precision
  });
};

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request body
    const body = await req.json();
    const { pdfId } = body;

    if (!pdfId) {
      return NextResponse.json({ error: 'PDF ID is required' }, { status: 400 });
    }

    // Fetch the PDF document
    const pdfDocument = await prisma.pDFDocument.findUnique({
      where: { id: pdfId },
      select: {
        id: true,
        fileName: true,
        userId: true
      }
    });

    if (!pdfDocument) {
      return NextResponse.json({ error: 'PDF document not found' }, { status: 404 });
    }

    // Verify the PDF document belongs to the user
    if (pdfDocument.userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to PDF document' }, { status: 403 });
    }

    // Use the adeloop namespace directly
    const namespace = 'adeloop';

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Get the PDF file from the request
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'PDF file is required' }, { status: 400 });
    }

    // Convert the file to an ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Load the PDF using PDFLoader
    const loader = new PDFLoader(buffer);
    const rawDocs = await loader.load();

    // Split the documents into chunks
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });
    const docs = await textSplitter.splitDocuments(rawDocs);

    // Add metadata to each document
    const documents = docs.map((doc, index) => {
      return new Document({
        pageContent: doc.pageContent,
        metadata: {
          ...doc.metadata,
          pdfId: pdfDocument.id,
          fileName: pdfDocument.fileName,
          chunkIndex: index,
          source: 'pdf'
        }
      });
    });

    // Log the documents being embedded for debugging
    console.log(`Embedding ${documents.length} chunks from PDF "${pdfDocument.fileName}" with metadata:`,
      documents.slice(0, 2).map(doc => doc.metadata)
    );

    // Create vector store
    await PineconeStore.fromDocuments(documents, embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
    });

    // Log success message
    console.log(`Successfully embedded ${documents.length} chunks from PDF "${pdfDocument.fileName}" into Pinecone namespace "${namespace}"`);

    // Update PDF document in database to mark as embedded
    await prisma.pDFDocument.update({
      where: { id: pdfId },
      data: {
        embedding: true,
        embeddingModel: 'cohere-embed-english-v3.0',
        vectorId: namespace
      }
    });

    return NextResponse.json({
      success: true,
      message: `Embedded ${documents.length} chunks from PDF "${pdfDocument.fileName}" into Pinecone`,
      namespace
    });
  } catch (error: any) {
    console.error('Error in PDF embedding API:', error);
    return NextResponse.json({
      success: false,
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
