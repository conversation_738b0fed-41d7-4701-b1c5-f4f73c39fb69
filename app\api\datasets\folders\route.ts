import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/db";

// GET - Fetch all folders or a specific folder with its contents
export async function GET(req: Request) {
  try {
    const { userId } = auth();
    const { searchParams } = new URL(req.url);
    const folderId = searchParams.get('folderId');

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    // If folderId is provided, return specific folder with its contents
    if (folderId) {
      const folder = await prisma.dataSetFolder.findUnique({
        where: { id: folderId },
        include: {
          children: true,
          datasets: {
            select: {
              id: true,
              name: true,
              description: true,
              fileType: true,
              createdAt: true,
              headers: true,
              data: true,
            }
          }
        }
      });

      if (!folder) {
        return NextResponse.json(
          { error: "Folder not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        folder
      });
    }

    // If no folderId, return all root folders (folders with no parent)
    const rootFolders = await prisma.dataSetFolder.findMany({
      where: { 
        userId: user.id,
        parentId: null
      },
      include: {
        children: true,
        datasets: {
          select: {
            id: true,
            name: true,
            description: true,
            fileType: true,
            createdAt: true,
            headers: true,
            data: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Also get datasets that are not in any folder (root datasets)
    const rootDatasets = await prisma.dataSet.findMany({
      where: { 
        userId: user.id,
        folderId: null
      },
      select: {
        id: true,
        name: true,
        description: true,
        fileType: true,
        createdAt: true,
        headers: true,
        data: true,
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      folders: rootFolders,
      datasets: rootDatasets
    });
  } catch (error) {
    console.error('Error fetching folders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch folders' },
      { status: 500 }
    );
  }
}

// POST - Create a new folder
export async function POST(req: Request) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    const { name, description, parentId } = await req.json();

    // Validate parent folder if provided
    if (parentId) {
      const parentFolder = await prisma.dataSetFolder.findUnique({
        where: { id: parentId }
      });

      if (!parentFolder) {
        return NextResponse.json(
          { error: "Parent folder not found" },
          { status: 404 }
        );
      }

      // Check if parent folder belongs to the user
      if (parentFolder.userId !== user.id) {
        return NextResponse.json(
          { error: "You don't have permission to access this folder" },
          { status: 403 }
        );
      }
    }

    // Create new folder
    const folder = await prisma.dataSetFolder.create({
      data: {
        name: name.trim(),
        description: description?.trim(),
        userId: user.id,
        parentId: parentId || null
      }
    });

    return NextResponse.json({
      success: true,
      folder
    });
  } catch (error) {
    console.error('Error creating folder:', error);
    return NextResponse.json(
      { error: 'Failed to create folder' },
      { status: 500 }
    );
  }
}

// PUT - Update a folder
export async function PUT(req: Request) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    const { folderId, name, description, parentId } = await req.json();

    if (!folderId) {
      return NextResponse.json(
        { error: "Folder ID is required" },
        { status: 400 }
      );
    }

    // Check if folder exists and belongs to the user
    const existingFolder = await prisma.dataSetFolder.findUnique({
      where: { id: folderId }
    });

    if (!existingFolder) {
      return NextResponse.json(
        { error: "Folder not found" },
        { status: 404 }
      );
    }

    if (existingFolder.userId !== user.id) {
      return NextResponse.json(
        { error: "You don't have permission to update this folder" },
        { status: 403 }
      );
    }

    // Update folder
    const updatedFolder = await prisma.dataSetFolder.update({
      where: { id: folderId },
      data: {
        name: name?.trim() || existingFolder.name,
        description: description?.trim() || existingFolder.description,
        parentId: parentId || existingFolder.parentId
      }
    });

    return NextResponse.json({
      success: true,
      folder: updatedFolder
    });
  } catch (error) {
    console.error('Error updating folder:', error);
    return NextResponse.json(
      { error: 'Failed to update folder' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a folder
export async function DELETE(req: Request) {
  try {
    const { userId } = auth();
    const { searchParams } = new URL(req.url);
    const folderId = searchParams.get('folderId');

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    if (!folderId) {
      return NextResponse.json(
        { error: "Folder ID is required" },
        { status: 400 }
      );
    }

    // Check if folder exists and belongs to the user
    const existingFolder = await prisma.dataSetFolder.findUnique({
      where: { id: folderId }
    });

    if (!existingFolder) {
      return NextResponse.json(
        { error: "Folder not found" },
        { status: 404 }
      );
    }

    if (existingFolder.userId !== user.id) {
      return NextResponse.json(
        { error: "You don't have permission to delete this folder" },
        { status: 403 }
      );
    }

    // Delete folder
    await prisma.dataSetFolder.delete({
      where: { id: folderId }
    });

    return NextResponse.json({
      success: true,
      message: "Folder deleted successfully"
    });
  } catch (error) {
    console.error('Error deleting folder:', error);
    return NextResponse.json(
      { error: 'Failed to delete folder' },
      { status: 500 }
    );
  }
}
