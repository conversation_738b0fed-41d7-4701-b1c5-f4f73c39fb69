'use client'

import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Database, RefreshCw, Upload, MessageSquare, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';

// Define Dataset type locally to avoid import issues
interface Dataset {
  id: string;
  name: string;
  data: any[];
  headers?: string[];
  fileType: string;
  createdAt: string | Date;
  description?: string;
  embedding?: boolean;
  embeddingModel?: string;
}

interface DatasetSelectorProps {
  selectedDataset: string | null;
  onSelectDataset: (datasetId: string) => void;
  onEmbedDataset: (datasetId: string) => Promise<boolean>;
  onDeleteEmbedding?: (datasetId: string) => Promise<boolean>;
  isEmbedding: boolean;
}

const DatasetSelector: React.FC<DatasetSelectorProps> = ({
  selectedDataset,
  onSelectDataset,
  onEmbedDataset,
  onDeleteEmbedding,
  isEmbedding
}) => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch datasets directly from the API
  const fetchDatasets = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/datasets', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch datasets: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.datasets) {
        console.log('Fetched datasets:', data.datasets);
        // Check if datasets have embedding field
        if (data.datasets.length > 0) {
          console.log('First dataset embedding status:', data.datasets[0].embedding);
        }
        setDatasets(data.datasets);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch datasets');
      console.error('Error fetching datasets:', err);
      toast.error(`Error fetching datasets: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch datasets on component mount
  useEffect(() => {
    fetchDatasets();
  }, []);

  const handleEmbedClick = async () => {
    if (selectedDataset) {
      const success = await onEmbedDataset(selectedDataset);
      if (success) {
        // Refresh datasets to update embedding status
        fetchDatasets();
      }
    }
  };

  const handleDeleteEmbeddingClick = async () => {
    if (selectedDataset && onDeleteEmbedding) {
      if (window.confirm('Are you sure you want to delete the embeddings for this dataset? This action cannot be undone.')) {
        const success = await onDeleteEmbedding(selectedDataset);
        if (success) {
          // Refresh datasets to update embedding status
          fetchDatasets();
          toast.success('Dataset embeddings deleted successfully');
        }
      }
    }
  };

  // Find the selected dataset object
  const selectedDatasetObj = datasets.find(d => d.id === selectedDataset);

  // Count embedded datasets - handle case where embedding field might be missing
  const embeddedDatasetsCount = datasets.filter(d => d.embedding === true).length;

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Database className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-medium">Dataset Selection</h3>
        </div>
        <Badge variant={embeddedDatasetsCount > 0 ? "success" : "outline"}>
          {embeddedDatasetsCount} Embedded
        </Badge>
      </div>

      <div className="flex flex-wrap items-center gap-2">
        <Select
          value={selectedDataset || ''}
          onValueChange={onSelectDataset}
          disabled={isLoading || isEmbedding}
        >
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Select a dataset" />
          </SelectTrigger>
          <SelectContent>
            {datasets.length === 0 ? (
              <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                No datasets available. Please upload a dataset first.
              </div>
            ) : (
              <>
                {/* Show embedded datasets first */}
                {datasets.filter(d => d.embedding === true).length > 0 && (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground font-medium">
                    Embedded Datasets
                  </div>
                )}
                {datasets.filter(d => d.embedding === true).map((dataset) => (
                  <SelectItem key={dataset.id} value={dataset.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{dataset.name}</span>
                      <Badge variant="outline" className="ml-2 bg-green-100 text-green-800">
                        Embedded
                      </Badge>
                    </div>
                  </SelectItem>
                ))}

                {/* Then show non-embedded datasets */}
                {datasets.filter(d => d.embedding !== true).length > 0 && (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground font-medium mt-1">
                    Other Datasets
                  </div>
                )}
                {datasets.filter(d => d.embedding !== true).map((dataset) => (
                  <SelectItem key={dataset.id} value={dataset.id}>
                    <span>{dataset.name}</span>
                  </SelectItem>
                ))}
              </>
            )}
          </SelectContent>
        </Select>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                onClick={fetchDatasets}
                disabled={isLoading || isEmbedding}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Refresh datasets</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {selectedDatasetObj && selectedDatasetObj.embedding !== true && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleEmbedClick}
                  disabled={isEmbedding || isLoading}
                  className="ml-2"
                >
                  {isEmbedding ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Embedding...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Embed Dataset
                    </>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Embed the selected dataset in the vector database</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {selectedDatasetObj && selectedDatasetObj.embedding === true && (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-2"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Ready to Chat
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>This dataset is already embedded and ready for chat</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {onDeleteEmbedding && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="ml-2"
                      onClick={handleDeleteEmbeddingClick}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Embeddings
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Delete the embeddings for this dataset from Pinecone</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </>
        )}
      </div>

      {selectedDatasetObj && (
        <div className="text-sm text-muted-foreground border p-3 rounded-md">
          <p>
            <span className="font-medium">Selected:</span> {selectedDatasetObj.name}
            {selectedDatasetObj.description && ` - ${selectedDatasetObj.description}`}
          </p>
          <p>
            <span className="font-medium">Rows:</span> {Array.isArray(selectedDatasetObj.data) ? selectedDatasetObj.data.length : 0}
            <span className="font-medium ml-3">Columns:</span> {selectedDatasetObj.headers?.length || 0}
            <span className="font-medium ml-3">Type:</span> {selectedDatasetObj.fileType}
          </p>
          <p>
            <span className="font-medium">Status:</span>
            {selectedDatasetObj.embedding === true ? (
              <span className="text-green-600 font-medium"> Embedded with {selectedDatasetObj.embeddingModel || 'Cohere'}</span>
            ) : (
              <span className="text-amber-600"> Not embedded yet</span>
            )}
          </p>
        </div>
      )}

      {error && (
        <div className="text-sm text-red-500 p-2 border border-red-200 rounded-md bg-red-50">
          {error}
        </div>
      )}
    </div>
  );
};

export default DatasetSelector;
