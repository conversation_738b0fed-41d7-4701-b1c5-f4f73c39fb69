import { useState, useEffect } from 'react';
import { toast } from 'sonner';

// Define types for LangChain RAG
export type LangChainChatMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string; // Must be a string
  deepResearch?: {
    iterations: any[];
    totalIterations: number;
    subQuestions: string[];
  };
};

export type LangChainModel =
  // Cohere models
  'command-r' | 'command-r-plus' |
  // Together AI models
  'llama-3-70b' | 'llama-3-8b' | 'mixtral-8x7b' | 'mistral-7b' | 'qwen-72b' | 'meta';

export type PineconeStatus = {
  isInitialized: boolean;
  recordCount: number;
  namespace: string;
};

// Hook for interacting with LangChain RAG
export const useLangChainRag = () => {
  const [selectedDataset, setSelectedDataset] = useState<string | null>(null);
  const [selectedPDF, setSelectedPDF] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEmbedding, setIsEmbedding] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<LangChainChatMessage[]>([]);
  const [selectedModel, setSelectedModel] = useState<LangChainModel>('command-r-plus');
  const [sourceDocuments, setSourceDocuments] = useState<any[]>([]);
  const [useDeepResearch, setUseDeepResearch] = useState<boolean>(false);
  const [maxIterations, setMaxIterations] = useState<number>(3);
  const [currentIterations, setCurrentIterations] = useState<number>(0);
  const [pineconeStatus, setPineconeStatus] = useState<PineconeStatus>({
    isInitialized: false,
    recordCount: 0,
    namespace: 'adeloop'
  });

  // Check Pinecone status on mount
  useEffect(() => {
    checkPineconeStatus();
  }, []);

  // Check Pinecone status
  const checkPineconeStatus = async () => {
    try {
      const response = await fetch('/api/langchain-rag/pinecone', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to check Pinecone status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        setPineconeStatus(data);
      }
    } catch (err: any) {
      console.error('Error checking Pinecone status:', err);
    }
  };

  // Embed dataset in Pinecone
  const embedDataset = async (datasetId: string) => {
    if (!datasetId) {
      setError('No dataset selected');
      return false;
    }

    setIsEmbedding(true);
    setError(null);

    try {
      const response = await fetch('/api/langchain-rag/embed', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          datasetId
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to embed dataset: ${response.status}`);
      }

      const data = await response.json();
      console.log('Embed response:', data);
      if (data.success) {
        toast.success('Dataset embedded successfully');
        checkPineconeStatus(); // Refresh status after embedding
        return true;
      } else {
        throw new Error(data.error || 'Failed to embed dataset');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to embed dataset');
      console.error('Error embedding dataset:', err);
      toast.error(`Error embedding dataset: ${err.message}`);
      return false;
    } finally {
      setIsEmbedding(false);
    }
  };

  // Embed PDF in Pinecone
  const embedPDF = async (pdfId: string, file: File) => {
    if (!pdfId) {
      setError('No PDF selected');
      return false;
    }

    if (!file) {
      setError('No PDF file provided');
      return false;
    }

    setIsEmbedding(true);
    setError(null);

    try {
      // Create form data to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('pdfId', pdfId);

      const response = await fetch('/api/langchain-rag/embed-pdf', {
        method: 'POST',
        credentials: 'include',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Failed to embed PDF: ${response.status}`);
      }

      const data = await response.json();
      console.log('Embed PDF response:', data);
      if (data.success) {
        toast.success('PDF embedded successfully');
        checkPineconeStatus(); // Refresh status after embedding
        return true;
      } else {
        throw new Error(data.error || 'Failed to embed PDF');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to embed PDF');
      console.error('Error embedding PDF:', err);
      toast.error(`Error embedding PDF: ${err.message}`);
      return false;
    } finally {
      setIsEmbedding(false);
    }
  };

  // Add a message to the chat
  const addMessage = (message: LangChainChatMessage) => {
    setChatMessages(prev => [...prev, message]);
  };

  // Clear chat messages
  const clearChat = () => {
    setChatMessages([]);
  };

  // Fetch datasets from the API
  const fetchDatasets = async () => {
    try {
      const response = await fetch('/api/datasets', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch datasets: ${response.status}`);
      }

      const data = await response.json();
      return data.datasets || [];
    } catch (err) {
      console.error('Error fetching datasets:', err);
      return [];
    }
  };

  // Fetch PDFs from the API
  const fetchPDFs = async () => {
    try {
      const response = await fetch('/api/pdf-documents', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch PDFs: ${response.status}`);
      }

      const data = await response.json();
      return data.documents || [];
    } catch (err) {
      console.error('Error fetching PDFs:', err);
      return [];
    }
  };

  // Delete embeddings from Pinecone
  const deleteEmbedding = async (type: 'dataset' | 'pdf', id: string) => {
    if (!id) {
      setError(`No ${type} selected`);
      return false;
    }

    try {
      const response = await fetch('/api/langchain-rag/delete-embedding', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          datasetId: type === 'dataset' ? id : undefined,
          pdfId: type === 'pdf' ? id : undefined,
          namespace: pineconeStatus.namespace
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to delete embeddings: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        toast.success(`${type === 'dataset' ? 'Dataset' : 'PDF'} embeddings deleted successfully`);
        checkPineconeStatus(); // Refresh status after deletion
        return true;
      } else {
        throw new Error(data.error || `Failed to delete ${type} embeddings`);
      }
    } catch (err: any) {
      setError(err.message || `Failed to delete ${type} embeddings`);
      console.error(`Error deleting ${type} embeddings:`, err);
      toast.error(`Error deleting embeddings: ${err.message}`);
      return false;
    }
  };

  // Send a message and get a response using RAG
  const sendMessage = async (message: string) => {
    // Check if we have at least one data source or if Pinecone is initialized
    const hasDataset = selectedDataset !== null;
    const hasPDF = selectedPDF !== null;
    const hasPinecone = pineconeStatus.isInitialized;

    if (!hasDataset && !hasPDF && !hasPinecone) {
      setError('No dataset or PDF selected or embedded');
      toast.error('Please select and embed a dataset or PDF first');
      return;
    }

    // Reset current iterations counter
    setCurrentIterations(0);

    // If Pinecone is initialized but no specific source is selected, we can use the entire namespace
    const useNamespaceOnly = !hasDataset && !hasPDF && hasPinecone;

    console.log('Sending message with configuration:', {
      hasDataset,
      hasPDF,
      hasPinecone,
      useNamespaceOnly,
      selectedDataset,
      selectedPDF
    });

    // Add user message to chat
    addMessage({ role: 'user', content: message });

    setIsLoading(true);
    setSourceDocuments([]);

    try {
      // Determine which endpoint to use based on whether deep research is enabled
      const endpoint = useDeepResearch
        ? '/api/langchain-rag/deep-research'
        : '/api/langchain-rag/chat';

      console.log(`Using ${useDeepResearch ? 'deep research' : 'standard'} RAG endpoint`);

      // Use our dedicated LangChain RAG chat API endpoint
      const response = await fetch(endpoint, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [
            ...chatMessages,
            { role: 'user', content: message }
          ],
          datasetId: selectedDataset,
          pdfId: selectedPDF,
          model: selectedModel,
          namespace: pineconeStatus.namespace,
          useNamespaceOnly,
          maxIterations: useDeepResearch ? maxIterations : undefined
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('LangChain RAG chat API error:', errorText);
        throw new Error(`Chat request failed: ${response.status}`);
      }

      const responseData = await response.json();

      if (responseData.error) {
        throw new Error(responseData.error);
      }

      // Store source documents for reference
      if (responseData.sourceDocuments && Array.isArray(responseData.sourceDocuments)) {
        setSourceDocuments(responseData.sourceDocuments);
      }

      // Add the assistant response to chat
      // Ensure content is a string
      const content = typeof responseData.content === 'string'
        ? responseData.content
        : JSON.stringify(responseData.content);

      // Check if we have deep research data
      const deepResearchData = responseData.deepResearch
        ? responseData.deepResearch
        : undefined;

      // If deep research was used, log the details and update iterations
      if (deepResearchData) {
        console.log('Deep Research results:', deepResearchData);

        // Update the current iterations count for the UI
        if (deepResearchData.totalIterations) {
          setCurrentIterations(deepResearchData.totalIterations);
        }
      }

      addMessage({
        role: 'assistant',
        content,
        deepResearch: deepResearchData
      });

    } catch (err: any) {
      setError(err.message || 'Failed to send message');
      console.error('Error sending message:', err);

      // Add error message to chat
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    selectedDataset,
    selectedPDF,
    isLoading,
    isEmbedding,
    error,
    chatMessages,
    selectedModel,
    pineconeStatus,
    sourceDocuments,
    useDeepResearch,
    maxIterations,
    currentIterations,
    setSelectedDataset,
    setSelectedPDF,
    setSelectedModel,
    setUseDeepResearch,
    setMaxIterations,
    embedDataset,
    embedPDF,
    deleteEmbedding,
    addMessage,
    clearChat,
    sendMessage,
    checkPineconeStatus
  };
};

export default useLangChainRag;
