"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, RPDefaultLayout, RPPages, RPConfig } from '@pdf-viewer/react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, Maximize2, Minimize2, Loader2 } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface PDFViewerProps {
  url: string;
  initialPage?: number;
  scale?: number;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  url,
  initialPage = 1,
  scale: initialScale = 1.0
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullScreen, setIsFullScreen] = useState(false);

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const downloadPDF = () => {
    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = url;
    link.download = url.split('/').pop() || 'document.pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDocumentLoadSuccess = () => {
    setLoading(false);
  };

  const handleDocumentLoadError = (error: any) => {
    console.error('Error loading PDF:', error);
    setError('Failed to load PDF document');
    setLoading(false);
  };

  const renderPDFViewer = () => {
    if (error) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-red-500">{error}</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-end p-2 border-b">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={downloadPDF}>
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={toggleFullScreen}>
              {isFullScreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        <div className="flex-1 relative">
          <RPConfig>
            <RPProvider
              src={url}
              withCredentials={false}
              onDocumentLoad={handleDocumentLoadSuccess}
              onError={handleDocumentLoadError}
              initialPage={initialPage}
              defaultScale={initialScale}
            >
              <RPDefaultLayout style={{ height: '100%', width: '100%' }}>
                <RPPages />
              </RPDefaultLayout>
            </RPProvider>
          </RPConfig>

          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">Loading PDF...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {isFullScreen ? (
        <Dialog open={isFullScreen} onOpenChange={setIsFullScreen}>
          <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden">
            {renderPDFViewer()}
          </DialogContent>
        </Dialog>
      ) : (
        renderPDFViewer()
      )}
    </>
  );
};

export default PDFViewer;
