// import ManagementMiniSidebar from "@/components/layout/ManagementMiniSidebar"
import { AppSidebar } from "@/components/sidebar/app-sidebar"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { Toaster } from "sonner"
import { useSidebar } from "@/components/ui/sidebar"

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-screen w-full">
        <AppSidebar className="z-[51] bg-background" />

        <div className="flex-1 w-full flex flex-col">
          <header className="sticky top-0 z-10 bg-background">
            {/* <SidebarTrigger /> */}
          </header>

          <main className="flex-1 w-full">
            {children}
          </main>
        </div>
      </div>
      <Toaster />
    </SidebarProvider>
  )
}


{/* <SidebarProvider defaultOpen={false}>
      <div className="flex min-h-screen bg-background/50 backdrop-blur-sm">
        <MainSidebar />

        <div
          className="flex-1 relative transition-all duration-500 ease-in-out will-change-transform"
          style={{
            marginLeft: "var(--sidebar-width, 4rem)",
            width: "calc(100% - var(--sidebar-width, 4rem))"
          }}
        >
          <div
            className="fixed top-0 h-full transition-all duration-500 ease-in-out z-20"
            style={{ left: "var(--sidebar-width, 4rem)" }}
          >
             <ManagementMiniSidebar />
          </div>

          <main className="min-h-screen transition-all duration-500 ease-in-out mini-sidebar-margin">
          <SidebarTrigger />
            {children}
          </main>
        </div>
      </div>
      <Toaster />
    </SidebarProvider> */}