"use client"

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { InfoIcon, Database, MessageSquare, Settings, FileSpreadsheet } from 'lucide-react';
import { useLangChainRag } from '@/hooks/useLangChainRag';
import DatasetSelector from './DatasetSelector';
import PDFSelector from './PDFSelector';
import ChatInterface from './ChatInterface';
import ModelSelector from './ModelSelector';
import CSVAnalyzer from './CSVAnalyzer';
import { toast } from 'sonner';

const LangChainRagInterface: React.FC = () => {
  const {
    selectedDataset,
    selectedPDF,
    isLoading,
    isEmbedding,
    error,
    chatMessages,
    selectedModel,
    pineconeStatus,
    sourceDocuments,
    useDeepResearch,
    maxIterations,
    currentIterations,
    setSelectedDataset,
    setSelectedPDF,
    setSelectedModel,
    setUseDeepResearch,
    setMaxIterations,
    embedDataset,
    embedPDF,
    deleteEmbedding,
    clearChat,
    sendMessage,
    checkPineconeStatus
  } = useLangChainRag();

  // Check for errors and display toast
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Periodically check Pinecone status
  useEffect(() => {
    const interval = setInterval(() => {
      if (pineconeStatus.isInitialized) {
        checkPineconeStatus();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [pineconeStatus.isInitialized]);

  return (
    <div className="container mx-auto p-4">
      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>LangChain RAG Chat</CardTitle>
              <CardDescription>
                Chat with your datasets using LangChain and Pinecone vector database
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant={pineconeStatus.isInitialized ? "default" : "outline"}
                className={pineconeStatus.isInitialized ? "bg-green-600" : ""}
              >
                <Database className="h-3 w-3 mr-1" />
                {pineconeStatus.isInitialized
                  ? `Pinecone: ${pineconeStatus.recordCount} records`
                  : "Pinecone: Not initialized"}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="chat" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="chat">
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="csv-analyzer">
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                CSV Analyzer
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
              <TabsTrigger value="info">
                <InfoIcon className="h-4 w-4 mr-2" />
                Info
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  <div className="space-y-6">
                    <DatasetSelector
                      selectedDataset={selectedDataset}
                      onSelectDataset={setSelectedDataset}
                      onEmbedDataset={embedDataset}
                      onDeleteEmbedding={(datasetId) => deleteEmbedding('dataset', datasetId)}
                      isEmbedding={isEmbedding}
                    />

                    <Separator />

                    <PDFSelector
                      selectedPDF={selectedPDF}
                      onSelectPDF={setSelectedPDF}
                      onEmbedPDF={embedPDF}
                      onDeleteEmbedding={(pdfId) => deleteEmbedding('pdf', pdfId)}
                      isEmbedding={isEmbedding}
                    />

                    <Separator />

                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Settings className="h-5 w-5 text-muted-foreground" />
                        <h3 className="text-lg font-medium">Model Selection</h3>
                      </div>

                      <ModelSelector
                        value={selectedModel}
                        onChange={setSelectedModel}
                        disabled={isLoading}
                      />
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Database className="h-5 w-5 text-muted-foreground" />
                        <h3 className="text-lg font-medium">Deep Research</h3>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="deep-research-toggle"
                            checked={useDeepResearch}
                            onChange={(e) => setUseDeepResearch(e.target.checked)}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                          <label htmlFor="deep-research-toggle" className="text-sm font-medium">
                            Enable Deep Research
                          </label>
                        </div>

                        <p className="text-xs text-muted-foreground">
                          Deep Research performs multiple iterations of retrieval to find more comprehensive answers for complex questions.
                        </p>

                        {useDeepResearch && (
                          <div className="mt-2">
                            <label htmlFor="max-iterations" className="text-sm font-medium block mb-1">
                              Max Iterations: {maxIterations}
                            </label>
                            <input
                              type="range"
                              id="max-iterations"
                              min="1"
                              max="5"
                              value={maxIterations}
                              onChange={(e) => setMaxIterations(parseInt(e.target.value))}
                              className="w-full"
                            />
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>Faster</span>
                              <span>More thorough</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2 h-[600px]">
                  <div className="flex flex-col h-full">
                    {/* Status indicator for debugging */}
                    {(selectedDataset || selectedPDF) && (
                      <div className="mb-2 p-2 bg-muted/30 rounded-md text-sm">
                        <p className="font-medium">Active Data Source:</p>
                        {selectedDataset && (
                          <p className="text-xs">Dataset ID: {selectedDataset}</p>
                        )}
                        {selectedPDF && (
                          <p className="text-xs">PDF ID: {selectedPDF}</p>
                        )}
                        <p className="text-xs mt-1">
                          Pinecone Status: {pineconeStatus.isInitialized ?
                            `Connected (${pineconeStatus.recordCount} records)` :
                            'Not connected'}
                        </p>
                      </div>
                    )}

                    <ChatInterface
                      messages={chatMessages}
                      onSendMessage={sendMessage}
                      onClearChat={clearChat}
                      isLoading={isLoading}
                      disabled={!pineconeStatus.isInitialized && selectedDataset === null && selectedPDF === null}
                      sourceDocuments={sourceDocuments}
                      useDeepResearch={useDeepResearch}
                      deepResearchIterations={currentIterations}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="csv-analyzer" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  <div className="space-y-6">
                    <DatasetSelector
                      selectedDataset={selectedDataset}
                      onSelectDataset={setSelectedDataset}
                      onEmbedDataset={embedDataset}
                      onDeleteEmbedding={(datasetId) => deleteEmbedding('dataset', datasetId)}
                      isEmbedding={isEmbedding}
                    />

                    <Alert>
                      <InfoIcon className="h-4 w-4" />
                      <AlertTitle>CSV Analyzer</AlertTitle>
                      <AlertDescription>
                        <p className="mt-2 text-sm">
                          The CSV Analyzer uses Python code execution to analyze your CSV data more effectively.
                          It can handle large datasets and perform complex calculations that regular RAG might struggle with.
                        </p>
                      </AlertDescription>
                    </Alert>
                  </div>
                </div>

                <div className="md:col-span-2 h-[600px]">
                  <CSVAnalyzer
                    datasetId={selectedDataset}
                    datasetName={selectedDataset ?
                      // Find dataset name from the database - this is a placeholder
                      "Selected Dataset" : null
                    }
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="mt-4 space-y-4">
              <Alert>
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>Vector Database Settings</AlertTitle>
                <AlertDescription>
                  <div className="mt-2 space-y-2">
                    <p>
                      <span className="font-medium">Status:</span>{' '}
                      {pineconeStatus.isInitialized ? 'Connected' : 'Not initialized'}
                    </p>
                    {pineconeStatus.isInitialized && (
                      <>
                        <p>
                          <span className="font-medium">Records:</span> {pineconeStatus.recordCount}
                        </p>
                        <p>
                          <span className="font-medium">Namespace:</span> {pineconeStatus.namespace}
                        </p>
                      </>
                    )}
                  </div>
                </AlertDescription>
              </Alert>

              <Alert>
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>Embedding Settings</AlertTitle>
                <AlertDescription>
                  <p className="mt-2">
                    Datasets are embedded using Cohere's <code>embed-english-v3.0</code> model for optimal semantic search.
                  </p>
                </AlertDescription>
              </Alert>
            </TabsContent>

            <TabsContent value="info" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>How It Works</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ol className="list-decimal pl-5 space-y-2 text-muted-foreground">
                      <li>
                        <strong>Select a dataset or PDF</strong> from your available files
                      </li>
                      <li>
                        <strong>Embed the content</strong> into Pinecone vector database
                      </li>
                      <li>
                        <strong>Ask questions</strong> about your data in natural language
                      </li>
                      <li>
                        <strong>Get insights</strong> powered by LangChain and LLMs with source references
                      </li>
                    </ol>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Benefits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                      <li>Query your data using natural language</li>
                      <li>Support for both CSV datasets and PDF documents</li>
                      <li>Get AI-powered insights with source references</li>
                      <li>Semantic search capabilities</li>
                      <li>Support for multiple LLM providers</li>
                      <li>Persistent vector storage for fast retrieval</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between text-sm text-muted-foreground">
          <div>Powered by LangChain.js, Pinecone, and AI models</div>
          <div>
            {selectedModel && (
              <span>
                Using: {selectedModel}
              </span>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default LangChainRagInterface;
