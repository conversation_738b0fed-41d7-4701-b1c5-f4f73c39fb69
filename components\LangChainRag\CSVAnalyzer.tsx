'use client'

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar } from '@/components/ui/avatar';
import { Loader2, Send, Database, Code, FileSpreadsheet } from 'lucide-react';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

interface CSVAnalyzerProps {
  datasetId: string | null;
  datasetName: string | null;
}

interface Message {
  role: 'user' | 'assistant';
  content: string;
  code?: string;
  executionResult?: string;
}

const CSVAnalyzer: React.FC<CSVAnalyzerProps> = ({ datasetId, datasetName }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading || !datasetId) return;

    const userMessage = input;
    setInput('');
    
    // Add user message to chat
    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setIsLoading(true);

    try {
      const response = await fetch('/api/langchain-rag/csv-analyzer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          datasetId: datasetId,
          model: 'command-r-plus', // Default model
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze CSV data');
      }

      const data = await response.json();
      
      // Add assistant message to chat
      setMessages(prev => [
        ...prev, 
        { 
          role: 'assistant', 
          content: data.content,
          code: data.codeExecuted,
          executionResult: data.executionResult
        }
      ]);
    } catch (error: any) {
      toast.error(`Error: ${error.message}`);
      // Add error message to chat
      setMessages(prev => [
        ...prev, 
        { 
          role: 'assistant', 
          content: `I encountered an error while analyzing the data: ${error.message}. Please try a different question or check if the dataset contains the information you're looking for.`
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <FileSpreadsheet className="h-5 w-5 mr-2 text-primary" />
          <h3 className="text-lg font-medium">CSV Analyzer</h3>
          {datasetName && (
            <span className="ml-2 text-sm text-muted-foreground">
              Dataset: {datasetName}
            </span>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={clearChat}
          disabled={messages.length === 0 || isLoading}
        >
          Clear Chat
        </Button>
      </div>

      <div className="flex-1 border rounded-md overflow-hidden flex flex-col">
        <Tabs defaultValue="chat" className="flex-1 flex flex-col" onValueChange={setActiveTab}>
          <div className="border-b px-4">
            <TabsList className="h-10">
              <TabsTrigger value="chat" className="data-[state=active]:bg-muted">Chat</TabsTrigger>
              <TabsTrigger value="code" className="data-[state=active]:bg-muted">Code</TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
            <ScrollArea className="flex-1 p-4">
              {messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
                  <Database className="h-12 w-12 mb-4 text-muted-foreground/50" />
                  <p>Ask questions about your CSV dataset!</p>
                  {!datasetId ? (
                    <p className="text-sm mt-2 text-amber-600">
                      Please select a dataset first to start analyzing.
                    </p>
                  ) : (
                    <p className="text-sm mt-2">
                      Try questions like "How many rows are in this dataset?", "What's the average of column X?", or "Find records where X equals Y"
                    </p>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={cn(
                        "flex items-start gap-3 rounded-lg p-4",
                        message.role === 'user'
                          ? "bg-primary/10 ml-10"
                          : "bg-muted/40 mr-10"
                      )}
                    >
                      <Avatar className="h-8 w-8">
                        <div className={cn(
                          "flex h-full w-full items-center justify-center rounded-full",
                          message.role === 'user' ? "bg-primary" : "bg-muted"
                        )}>
                          {message.role === 'user' ? 'U' : 'AI'}
                        </div>
                      </Avatar>
                      <div className="flex-1 overflow-hidden">
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              code({ node, className, children, ...props }: any) {
                                const match = /language-(\w+)/.exec(className || '');
                                const isInline = !match;
                                return !isInline && match ? (
                                  <SyntaxHighlighter
                                    language={match[1]}
                                    // @ts-ignore - Known issue with type definitions
                                    style={vscDarkPlus}
                                    PreTag="div"
                                    {...props}
                                  >
                                    {String(children).replace(/\n$/, '')}
                                  </SyntaxHighlighter>
                                ) : (
                                  <code className={className} {...props}>
                                    {String(children)}
                                  </code>
                                );
                              }
                            }}
                          >
                            {message.content}
                          </ReactMarkdown>
                          
                          {message.role === 'assistant' && message.code && (
                            <div className="mt-2">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="text-xs"
                                onClick={() => setActiveTab('code')}
                              >
                                <Code className="h-3 w-3 mr-1" />
                                View Code
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
            
            <div className="p-4 border-t">
              <div className="flex items-center gap-2">
                <Input
                  placeholder={datasetId ? "Ask a question about your CSV data..." : "Select a dataset first"}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  disabled={isLoading || !datasetId}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!input.trim() || isLoading || !datasetId}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="code" className="flex-1 flex flex-col p-0 m-0">
            <ScrollArea className="flex-1 p-4">
              {messages.length === 0 || !messages.some(m => m.code) ? (
                <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
                  <Code className="h-12 w-12 mb-4 text-muted-foreground/50" />
                  <p>No code has been generated yet</p>
                  <p className="text-sm mt-2">
                    Ask a question about your CSV data to see the Python code used for analysis
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  {messages
                    .filter(message => message.role === 'assistant' && message.code)
                    .map((message, index) => (
                      <Card key={index}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Python Code</CardTitle>
                          <CardDescription>
                            Generated to answer: "{messages[messages.findIndex(m => m === message) - 1]?.content}"
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="mb-4">
                            <SyntaxHighlighter
                              language="python"
                              // @ts-ignore - Known issue with type definitions
                              style={vscDarkPlus}
                            >
                              {message.code || ''}
                            </SyntaxHighlighter>
                          </div>
                          
                          {message.executionResult && (
                            <>
                              <div className="text-sm font-medium mb-2">Execution Result:</div>
                              <div className="bg-muted p-3 rounded-md overflow-x-auto whitespace-pre-wrap text-xs font-mono">
                                {message.executionResult}
                              </div>
                            </>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CSVAnalyzer;
