import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf';
import { getDocument } from 'pdfjs-dist/legacy/build/pdf';

// Initialize the worker
if (typeof window === 'undefined') {
  // Server-side
  const pdfjsWorker = require('pdfjs-dist/legacy/build/pdf.worker.js');
  pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
} else {
  // Client-side
  pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

export async function extractTextFromPdf(arrayBuffer: ArrayBuffer) {
  try {
    const pdf = await getDocument({ data: arrayBuffer }).promise;
    const numPages = pdf.numPages;
    const pages = [];

    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const text = textContent.items
        .map((item: any) => item.str)
        .join(' ')
        .trim();

      pages.push({
        pageNumber: i,
        text,
      });
    }

    return pages;
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    throw error;
  }
} 