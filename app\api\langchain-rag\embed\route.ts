import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { Document } from '@langchain/core/documents';
import prisma from '@/lib/db';

// Initialize Pinecone client
const getPineconeClient = () => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings with 1024 dimensions to match llama-text-embed-v2
const getEmbeddings = () => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    api<PERSON><PERSON>,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_document', // For document embeddings
    truncate: 'END', // Truncate long texts from the end
    embeddingFormat: 'float' // Use float format for better precision
  });
};

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request body
    const body = await req.json();
    const { datasetId } = body;

    if (!datasetId) {
      return NextResponse.json({ error: 'Dataset ID is required' }, { status: 400 });
    }

    // Fetch the dataset
    const dataset = await prisma.dataSet.findUnique({
      where: { id: datasetId },
      select: {
        id: true,
        name: true,
        data: true,
        headers: true,
        fileType: true,
        userId: true
      }
    });

    if (!dataset) {
      return NextResponse.json({ error: 'Dataset not found' }, { status: 404 });
    }

    // Verify the dataset belongs to the user
    if (dataset.userId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to dataset' }, { status: 403 });
    }

    // Use the adeloop namespace directly
    const namespace = 'adeloop';

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Convert dataset to documents for embedding
    const documents: Document[] = [];

    // Handle array data (typical for CSV/Excel datasets)
    if (Array.isArray(dataset.data)) {
      dataset.data.forEach((row: any, rowIndex: number) => {
        // Convert row to string representation
        const rowContent = dataset.headers
          .map(header => `${header}: ${row[header]}`)
          .join('\n');

        documents.push(
          new Document({
            pageContent: rowContent,
            metadata: {
              datasetId: dataset.id,
              datasetName: dataset.name,
              rowIndex,
              source: 'dataset'
            }
          })
        );
      });
    }

    // Log the documents being embedded for debugging
    console.log(`Embedding ${documents.length} documents from dataset "${dataset.name}" with metadata:`,
      documents.slice(0, 2).map(doc => doc.metadata)
    );

    // Create vector store
    await PineconeStore.fromDocuments(documents, embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
    });

    // Log success message
    console.log(`Successfully embedded ${documents.length} documents from dataset "${dataset.name}" into Pinecone namespace "${namespace}"`);

    // Update dataset in database to mark as embedded
    await prisma.dataSet.update({
      where: { id: datasetId },
      data: {
        embedding: true,
        embeddingModel: 'llama-text-embed-v2'
      }
    });

    return NextResponse.json({
      success: true,
      message: `Embedded ${documents.length} documents from dataset "${dataset.name}" into Pinecone`,
      namespace
    });
  } catch (error: any) {
    console.error('Error in embedding API:', error);
    return NextResponse.json({
      success: false,
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}




