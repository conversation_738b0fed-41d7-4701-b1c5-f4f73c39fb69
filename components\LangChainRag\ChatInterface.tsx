"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar } from '@/components/ui/avatar';
import { Loader2, Send, Trash2, BrainCircuit } from 'lucide-react';
import { LangChainChatMessage } from '@/hooks/useLangChainRag';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/lib/utils';
import SourceReference from './SourceReference';
import ThinkingAnimation from './ThinkingAnimation';

interface ChatInterfaceProps {
  messages: LangChainChatMessage[];
  onSendMessage: (message: string) => Promise<void>;
  onClearChat: () => void;
  isLoading: boolean;
  disabled?: boolean;
  sourceDocuments?: any[];
  useDeepResearch?: boolean;
  deepResearchIterations?: number;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  onClearChat,
  isLoading,
  disabled = false,
  sourceDocuments = [],
  useDeepResearch = false,
  deepResearchIterations = 0
}) => {
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    if (!disabled) {
      inputRef.current?.focus();
    }
  }, [disabled]);

  const handleSendMessage = async () => {
    if (input.trim() && !isLoading && !disabled) {
      const message = input;
      setInput('');
      await onSendMessage(message);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Chat</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={onClearChat}
          disabled={messages.length === 0 || isLoading || disabled}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Chat
        </Button>
      </div>

      <div className="flex-1 border rounded-md overflow-hidden flex flex-col">
        <ScrollArea className="flex-1 p-4">
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
              <p>No messages yet. Start a conversation!</p>
              {disabled ? (
                <p className="text-sm mt-2 text-amber-600">
                  Please select and embed a dataset or PDF document first to start chatting.
                </p>
              ) : (
                <p className="text-sm mt-2">
                  Ask questions about your data to get insights with source references.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Thinking animation for deep research */}
              {isLoading && useDeepResearch && (
                <ThinkingAnimation
                  isVisible={true}
                  iterations={deepResearchIterations}
                  className="ml-10"
                />
              )}

              {messages.map((message, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex items-start gap-3 rounded-lg p-4",
                    message.role === 'user'
                      ? "bg-primary/10 ml-10"
                      : "bg-muted/40 mr-10"
                  )}
                >
                  <Avatar className="h-8 w-8">
                    <div className={cn(
                      "flex h-full w-full items-center justify-center rounded-full",
                      message.role === 'user' ? "bg-primary" : "bg-muted"
                    )}>
                      {message.role === 'user' ? 'U' : 'AI'}
                    </div>
                  </Avatar>
                  <div className="flex-1 overflow-hidden">
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          code({ node, className, children, ...props }: any) {
                            const match = /language-(\w+)/.exec(className || '');
                            const isInline = !match;
                            return !isInline && match ? (
                              <SyntaxHighlighter
                                language={match[1]}
                                // @ts-ignore - Known issue with type definitions
                                style={vscDarkPlus}
                                PreTag="div"
                                {...props}
                              >
                                {String(children).replace(/\n$/, '')}
                              </SyntaxHighlighter>
                            ) : (
                              <code className={className} {...props}>
                                {String(children)}
                              </code>
                            );
                          }
                        }}
                      >
                        {typeof message.content === 'string'
                          ? message.content
                          : JSON.stringify(message.content)}
                      </ReactMarkdown>

                      {/* Show source references for assistant messages */}
                      {message.role === 'assistant' && sourceDocuments.length > 0 && (
                        <SourceReference sourceDocuments={sourceDocuments} />
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>

        <div className="p-4 border-t">
          <div className="flex items-center gap-2">
            <Input
              ref={inputRef}
              placeholder="Type your message..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isLoading || disabled}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading || disabled}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
