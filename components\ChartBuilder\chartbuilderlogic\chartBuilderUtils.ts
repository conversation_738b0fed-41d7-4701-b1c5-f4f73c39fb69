import { DefaultContentLanguage } from './types';

// Safely check for browser environment
export const isBrowser = typeof window !== 'undefined';

/**
 * Check for internet connectivity
 * @returns Promise<boolean> - True if connected, false otherwise
 */
export const checkInternetConnection = async (): Promise<boolean> => {
  if (!isBrowser) return true; // Assume we have connectivity on server

  try {
    // Try to fetch a small resource to check connectivity
    const controller = new AbortController();
    const signal = controller.signal;
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch('/api/ping', {
      method: 'HEAD', // Just get headers, not the body
      cache: 'no-store', // Don't use cached responses
      signal, // Use the abort signal for timeout
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.log('Internet connection check failed:', error);
    return false;
  }
};

/**
 * Check if SQL query has GraphicWalker comment
 * @param code SQL query string
 * @returns boolean
 */
export const hasGraphicWalkerComment = (code: string): boolean => {
  return code.includes("--#graphicwalker") || code.includes("-- #graphicwalker");
};

/**
 * Check if SQL query has loopchart command
 * @param code SQL query string
 * @returns boolean
 */
export const hasLoopchartCommand = (code: string): boolean => {
  return code.includes("--loopchart") || code.includes("-- loopchart");
};

/**
 * Get default content for a cell based on language
 * @param language The programming language
 * @returns Default content string
 */
export const getDefaultContent = (language: DefaultContentLanguage): string => {
  switch (language) {
    case 'python':
      return `# Dataset is available as 'df' (pandas DataFrame)
# Available libraries: pandas as pd, numpy as np, matplotlib.pyplot as plt, seaborn as sns

# Example 1: View the first few rows of your data
print(f"Dataset shape: {df.shape}")
print(f"Available columns: {df.columns.tolist()}")
result = df.head()

# Example 2: Create a simple plot (uncomment to use)
"""
import matplotlib.pyplot as plt
import numpy as np

# Create some data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create a plot
plt.figure(figsize=(10, 6))
plt.plot(x, y)
plt.title('Sine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.grid(True)

# IMPORTANT: This line is required to display the plot
result = get_plot()
"""

# Example 3: Simple one-line plot (uncomment to use)
"""
# Create a plot with a single line of code
result = plot_histogram('age', bins=10)
"""`;
    case 'javascript':
      return `// Dataset is available as "df" with helper methods
// Examples:

// Get first 5 rows
result = df.head()`;
    case 'markdown':
      return `# Markdown Cell

This is a markdown cell where you can write formatted text.

## Features:
- **Bold text** and _italic text_
- Bulleted lists like this one
- Numbered lists
1. First item
2. Second item

## Tables
| Column 1 | Column 2 |
|----------|----------|
| Cell 1   | Cell 2   |

---
Double-click to edit this cell`;
    default:
      return '-- Write your SQL query here\nSELECT * FROM dataset1 LIMIT 5';
  }
};

/**
 * Initialize alasql-utils module
 * @returns Promise<boolean> - True if initialized successfully
 */
export const initializeAlasqlUtils = async (): Promise<boolean> => {
  if (isBrowser) {
    try {
      const module = await import('@/lib/alasql-utils');
      return true;
    } catch (error) {
      console.error('Failed to load alasql-utils:', error);
      return false;
    }
  }
  return false;
};
