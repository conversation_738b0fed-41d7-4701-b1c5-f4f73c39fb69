import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { nanoid } from 'nanoid';
import { useDashboardStore } from '@/lib/dashboardStore';
import { SavedChart, TableItem, PythonPlotItem, CalculatorResultItem } from '../DashboardSection/types';
import { UseChartSavingReturn } from './types';

/**
 * Hook for handling chart saving functionality in the ChartBuilder
 * @returns Chart saving functions
 */
export const useChartSaving = (): UseChartSavingReturn => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Use Zustand store for dashboard items
  const {
    charts: savedCharts,
    addChart,
    updateChart,
    removeChart,
    addTable,
    addPlot,
    addCalculatorResult
  } = useDashboardStore();

  // Handle saving chart to dashboard
  const handleSaveChart = (chartData: any, chartConfig: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => {
    // Check if we already have this chart in our state
    const existingChartIndex = savedCharts.findIndex(chart =>
      chartId ? chart.id === chartId : false
    );

    // If we found an existing chart, use its position; otherwise calculate a new position
    const { gridColumn, gridRow, width, height } = existingChartIndex >= 0
      ? {
          gridColumn: savedCharts[existingChartIndex].gridColumn,
          gridRow: savedCharts[existingChartIndex].gridRow,
          width: savedCharts[existingChartIndex].width,
          height: savedCharts[existingChartIndex].height
        }
      : (() => {
          // Calculate the next available position in the grid
          if (savedCharts.length === 0) {
            return { gridColumn: 0, gridRow: 0, width: 4, height: 3 };
          }

          // Find the maximum row position
          const maxRow = Math.max(...savedCharts.map(chart => chart.gridRow + chart.height));
          return { gridColumn: 0, gridRow: maxRow, width: 4, height: 3 };
        })();

    // Create new chart with complete configuration and default size
    // Use the provided chartId if available, otherwise generate a new one
    const newChart: SavedChart = {
      id: chartId || nanoid(),
      title: chartConfig.title || 'Untitled Chart',
      description: chartConfig.description || 'No description',
      chartType,
      data: chartData,
      config: {
        // Include all original configuration
        ...chartConfig,
        // Ensure chartType is set
        type: chartType,
      },
      createdAt: new Date(),
      size: 'medium', // Default size
      // Required properties from DashboardSavedChart
      type: 'chart',
      gridColumn,
      gridRow,
      width,
      height
    };

    // Add to the Zustand store - this is now the single source of truth
    addChart(newChart);

    // Only show toast for new charts, not updates
    if (existingChartIndex === -1) {
      toast.success(`Chart "${newChart.title}" saved to dashboard`);
    } else {
      toast.success(`Chart "${newChart.title}" updated`);
    }
  };

  // Handle saving table to dashboard
  const handleSaveTable = (tableData: any[], columns: string[], tableId?: string) => {
    // Get all dashboard items from the store
    const { charts: savedCharts, tables: savedTables, plots: savedPlots } = useDashboardStore.getState();

    // Calculate the next available position in the grid
    const { gridColumn, gridRow } = (() => {
      if (savedCharts.length === 0 && savedTables.length === 0 && savedPlots.length === 0) {
        return { gridColumn: 0, gridRow: 0 };
      }

      // Find the maximum row position across all items
      const maxRowCharts = savedCharts.length > 0
        ? Math.max(...savedCharts.map(chart => chart.gridRow + chart.height))
        : 0;
      const maxRowTables = savedTables.length > 0
        ? Math.max(...savedTables.map(table => table.gridRow + table.height))
        : 0;
      const maxRowPlots = savedPlots.length > 0
        ? Math.max(...savedPlots.map(plot => plot.gridRow + plot.height))
        : 0;

      return { gridColumn: 0, gridRow: Math.max(maxRowCharts, maxRowTables, maxRowPlots) };
    })();

    // Create a new table object with all required properties
    const newTable: TableItem = {
      id: tableId || `table-${nanoid()}`,
      type: 'table' as const, // Use const assertion to ensure it's the literal 'table'
      title: `Table (${columns.length} columns, ${tableData.length} rows)`,
      description: `Data table with ${tableData.length} rows`,
      data: tableData,
      columns: columns,
      gridColumn,
      gridRow,
      width: 6,
      height: 4,
      createdAt: new Date()
    };

    // Add to the Zustand store - this is now the single source of truth
    addTable(newTable);

    toast.success(`Table saved to dashboard`);

    // Switch to dashboard tab
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', 'dashboard');
    router.push(`?${params.toString()}`);
  };

  // Handle saving plot to dashboard
  const handleSavePlot = (plotUrl: string, plotIndex: number, plotId?: string) => {
    // Get all dashboard items from the store
    const { charts: savedCharts, tables: savedTables, plots: savedPlots } = useDashboardStore.getState();

    // Calculate the next available position in the grid
    const { gridColumn, gridRow } = (() => {
      if (savedCharts.length === 0 && savedTables.length === 0 && savedPlots.length === 0) {
        return { gridColumn: 0, gridRow: 0 };
      }

      // Find the maximum row position across all items
      const maxRowCharts = savedCharts.length > 0
        ? Math.max(...savedCharts.map(chart => chart.gridRow + chart.height))
        : 0;
      const maxRowTables = savedTables.length > 0
        ? Math.max(...savedTables.map(table => table.gridRow + table.height))
        : 0;
      const maxRowPlots = savedPlots.length > 0
        ? Math.max(...savedPlots.map(plot => plot.gridRow + plot.height))
        : 0;

      return { gridColumn: 0, gridRow: Math.max(maxRowCharts, maxRowTables, maxRowPlots) };
    })();

    // Create a new plot object with all required properties
    const newPlot: PythonPlotItem = {
      id: plotId || `plot-${nanoid()}`,
      type: 'pythonplot' as const, // Use const assertion to ensure it's the literal 'pythonplot'
      title: `Plot ${plotIndex + 1}`,
      description: `Python plot visualization`,
      plotUrl: plotUrl,
      gridColumn,
      gridRow,
      width: 6,
      height: 4,
      createdAt: new Date()
    };

    // Add to the Zustand store - this is now the single source of truth
    addPlot(newPlot);

    toast.success(`Plot ${plotIndex + 1} saved to dashboard`);

    // Switch to dashboard tab
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', 'dashboard');
    router.push(`?${params.toString()}`);
  };

  // Remove a chart from dashboard
  const handleRemoveChart = (chartId: string) => {
    // Remove from the Zustand store
    removeChart(chartId);
    toast.success("Chart removed from dashboard");
  };

  // Update chart properties
  const handleUpdateChart = (chartId: string, updatedProps: Partial<SavedChart>) => {
    // Use the Zustand store's updateChart function
    updateChart(chartId, updatedProps);
  };

  // Handle reordering charts
  const handleReorderCharts = (reorderedCharts: any[]) => {
    // Ensure all charts have the required properties
    const validatedCharts: SavedChart[] = reorderedCharts.map(chart => ({
      ...chart,
      type: 'chart' as const,
      title: chart.title || 'Untitled Chart',
      description: chart.description || '',
      gridColumn: chart.gridColumn || 0,
      gridRow: chart.gridRow || 0,
      width: chart.width || 4,
      height: chart.height || 3,
      chartType: chart.chartType || 'bar',
      data: chart.data || [],
      config: chart.config || {},
      createdAt: chart.createdAt instanceof Date ? chart.createdAt : new Date()
    }));

    // Update each chart in the store
    validatedCharts.forEach(chart => {
      addChart(chart);
    });
  };

  // Handle saving calculator result to dashboard
  const handleSaveCalculatorResult = (formula: string, result: any, title?: string, description?: string, resultId?: string) => {
    console.log('🧮 Saving calculator result:', { formula, result, title, description });

    // Get all dashboard items from the store
    const { charts: savedCharts, tables: savedTables, plots: savedPlots, calculatorResults: savedResults } = useDashboardStore.getState();

    console.log('🧮 Current store state:', { savedCharts: savedCharts.length, savedTables: savedTables.length, savedPlots: savedPlots.length, savedResults: savedResults.length });

    // Calculate next available position
    const allItems = [...savedCharts, ...savedTables, ...savedPlots, ...savedResults];
    const maxRow = allItems.length > 0 ? Math.max(...allItems.map(item => item.gridRow + item.height)) : 0;
    const gridColumn = 0;
    const gridRow = maxRow;

    // Determine result type
    let resultType: 'number' | 'text' | 'error' = 'text';
    if (result === null || result === undefined) {
      resultType = 'error';
    } else if (typeof result === 'number') {
      resultType = 'number';
    }

    // Format result for display
    const formatResult = (res: any) => {
      if (res === null || res === undefined) return 'No result';
      if (typeof res === 'number') {
        if (Number.isInteger(res)) return res.toString();
        if (Math.abs(res) >= 1000000) return res.toExponential(2);
        return res.toFixed(6).replace(/\.?0+$/, '');
      }
      return String(res);
    };

    // Create a new calculator result object with all required properties
    const newCalculatorResult: CalculatorResultItem = {
      id: resultId || `calculator-${nanoid()}`,
      type: 'calculator' as const,
      title: title || `${formula.length > 20 ? formula.substring(0, 20) + '...' : formula}`,
      description: description || `Result: ${formatResult(result)}`,
      formula: formula,
      result: result,
      resultType: resultType,
      formattedResult: formatResult(result),
      timestamp: Date.now(),
      gridColumn,
      gridRow,
      width: 3, // Make calculator cards smaller (3 instead of 4)
      height: 3, // Keep height compact
      createdAt: new Date()
    };

    console.log('🧮 Created calculator result object:', newCalculatorResult);

    // Add to the Zustand store
    addCalculatorResult(newCalculatorResult);

    console.log('🧮 Added to store, new calculator results count:', useDashboardStore.getState().calculatorResults.length);

    toast.success(`Calculator result saved to dashboard`);

    // Switch to dashboard tab
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', 'dashboard');
    router.push(`?${params.toString()}`);
  };

  return {
    handleSaveChart,
    handleSaveTable,
    handleSavePlot,
    handleSaveCalculatorResult,
    handleRemoveChart,
    handleUpdateChart,
    handleReorderCharts
  };
};
