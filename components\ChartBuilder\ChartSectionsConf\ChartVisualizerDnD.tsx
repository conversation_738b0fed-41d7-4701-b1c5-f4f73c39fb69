'use client'

import { useState, useEffect, useMemo, useRef } from 'react'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import './styles/drag-performance.css'
import {
  LineChart as LineChartIcon, BarChart3,
  <PERSON><PERSON>hart as PieChartIcon, TrendingUp,
  Settings, Palette, Filter, RefreshCw,
  ArrowUpDown, Layers, X, Plus, Pin, PanelTop
} from "lucide-react"
import { toast } from "sonner"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { DraggableItem, DropZone, ColumnsList } from './DragDropComponents'
import { Zoomable<PERSON><PERSON> } from "./ZoomableChart"
import { ColorPicker } from "./ColorPicker"
import { MultiSeriesChart } from "./MultiSeriesChart"
import { ChartConfigExporter, ChartConfigExport } from "./ChartConfigExporter"

// Keep the same props interface from the original component
interface ChartVisualizerProps {
  data: Record<string, any>[] | undefined;
  initialChartType: 'line' | 'bar' | 'pie' | 'area';
  chartConfig: {
    xAxis: string;
    yAxis: string;
    title: string;
    description: string;
    showLegend: boolean;
    showLabels: boolean;
    showGrid: boolean;
    color?: string;
    customLabel?: string;
    type?: 'line' | 'bar' | 'pie' | 'area';
    aggregation?: string;
    groupBy?: string;
    timeScale?: string;
    enableZoom?: boolean;
    multiSeries?: boolean;
  };
  showConfig: boolean;
  fullHeight?: boolean;
  onConfigChange?: (newConfig: any) => void;
  cellId?: string;
}

type ChartType = 'line' | 'bar' | 'pie' | 'area'
type AggregationType = 'sum' | 'average' | 'min' | 'max' | 'count' | 'none'
type TimeScaleType = 'day' | 'week' | 'month' | 'year' | 'none'
type FilterOperator = 'equals' | 'notEquals' | 'greaterThan' | 'lessThan' | 'contains'

interface Filter {
  column: string;
  operator: FilterOperator;
  value: string | number;
  enabled: boolean;
}

interface ChartConfig {
  type: ChartType;
  xAxis: string;
  yAxis: string;
  title: string;
  description: string;
  color: string;
  showLegend: boolean;
  showLabels: boolean;
  showGrid: boolean;
  aggregation: AggregationType;
  groupBy: string;
  timeScale: TimeScaleType;
  customLabel: string;
  enableZoom: boolean;
  multiSeries: boolean;
}

interface Series {
  id: string;
  field: string;
  color: string;
  label: string;
  visible: boolean;
}

// Chart color palette
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
];

export function ChartVisualizerDnD({
  data,
  initialChartType = 'line',
  chartConfig: initialConfig,
  showConfig = false,
  fullHeight = false,
  onConfigChange,
  cellId
}: ChartVisualizerProps) {
  const safeData = data || [];

  // State for chart configuration (same as original)
  const [config, setConfig] = useState<ChartConfig>({
    type: (initialConfig?.type as ChartType) || initialChartType,
    xAxis: initialConfig?.xAxis || '',
    yAxis: initialConfig?.yAxis || '',
    title: initialConfig?.title || 'Chart Visualization',
    description: initialConfig?.description || 'Data visualization',
    color: initialConfig?.color || COLORS[0],
    showLegend: initialConfig?.showLegend !== undefined ? initialConfig.showLegend : true,
    showLabels: initialConfig?.showLabels !== undefined ? initialConfig.showLabels : false,
    showGrid: initialConfig?.showGrid !== undefined ? initialConfig.showGrid : true,
    aggregation: validateAggregationType(initialConfig?.aggregation),
    groupBy: initialConfig?.groupBy || 'none',
    timeScale: validateTimeScaleType(initialConfig?.timeScale),
    customLabel: initialConfig?.customLabel || '',
    enableZoom: initialConfig?.enableZoom !== undefined ? initialConfig.enableZoom : true,
    multiSeries: initialConfig?.multiSeries !== undefined ? initialConfig.multiSeries : false
  });

  // Helper functions to validate enum types (same as original)
  function validateAggregationType(value?: string): AggregationType {
    const validValues: AggregationType[] = ['sum', 'average', 'min', 'max', 'count', 'none'];
    return validValues.includes(value as AggregationType)
      ? (value as AggregationType)
      : 'none';
  }

  function validateTimeScaleType(value?: string): TimeScaleType {
    const validValues: TimeScaleType[] = ['day', 'week', 'month', 'year', 'none'];
    return validValues.includes(value as TimeScaleType)
      ? (value as TimeScaleType)
      : 'none';
  }

  function validateChartType(value?: string): ChartType {
    const validValues: ChartType[] = ['line', 'bar', 'pie', 'area'];
    return validValues.includes(value as ChartType)
      ? (value as ChartType)
      : 'line';
  }

  function validateFilterOperator(value: string): FilterOperator {
    const validValues: FilterOperator[] = ['equals', 'notEquals', 'greaterThan', 'lessThan', 'contains'];
    return validValues.includes(value as FilterOperator)
      ? (value as FilterOperator)
      : 'equals';
  }

  // State for filtering (same as original)
  const [filters, setFilters] = useState<Filter[]>([]);

  // Add state for data series in multi-series mode (same as original)
  const [series, setSeries] = useState<Series[]>([]);

  // New state for drag and drop
  const [activeTab, setActiveTab] = useState('chart');

  // Dropzone states
  const [xAxisColumns, setXAxisColumns] = useState<DraggableItem[]>([]);
  const [yAxisColumns, setYAxisColumns] = useState<DraggableItem[]>([]);
  const [groupByColumns, setGroupByColumns] = useState<DraggableItem[]>([]);
  const [filterColumns, setFilterColumns] = useState<DraggableItem[]>([]);
  const [colorByColumns, setColorByColumns] = useState<DraggableItem[]>([]);
  const [labelColumns, setLabelColumns] = useState<DraggableItem[]>([]);

  // Data columns detection (same as original)
  const columns = useMemo(() =>
    safeData.length > 0 ? Object.keys(safeData[0]) : [],
    [safeData]
  );

  const numericColumns = useMemo(() =>
    safeData.length > 0
      ? columns.filter(col => typeof safeData[0][col] === 'number')
      : [],
    [safeData, columns]
  );

  const nonNumericColumns = useMemo(() =>
    safeData.length > 0
      ? columns.filter(col => typeof safeData[0][col] !== 'number')
      : [],
    [safeData, columns]
  );

  const dateColumns = useMemo(() =>
    safeData.length > 0
      ? columns.filter(col => {
          const val = safeData[0][col];
          return typeof val === 'string' &&
                (val.match(/^\d{4}-\d{2}-\d{2}/) ||
                 val.match(/^\d{2}\/\d{2}\/\d{4}/));
        })
      : [],
    [safeData, columns]
  );

  // Convert columns to draggable items
  const draggableColumns = useMemo(() => {
    return columns.map(col => {
      let dataType: 'string' | 'number' | 'date' | 'boolean' = 'string';

      if (numericColumns.includes(col)) {
        dataType = 'number';
      } else if (dateColumns.includes(col)) {
        dataType = 'date';
      } else if (safeData.length > 0 && typeof safeData[0][col] === 'boolean') {
        dataType = 'boolean';
      }

      return {
        id: col,
        name: col,
        type: 'column',
        dataType
      };
    });
  }, [columns, numericColumns, dateColumns, safeData]);

  // Track used column IDs to filter available ones
  const usedColumnIds = useMemo(() => {
    const ids: string[] = [];

    xAxisColumns.forEach(col => ids.push(col.id));
    yAxisColumns.forEach(col => ids.push(col.id));
    groupByColumns.forEach(col => ids.push(col.id));
    colorByColumns.forEach(col => ids.push(col.id));
    labelColumns.forEach(col => ids.push(col.id));

    // We allow the same column to be in filters multiple times
    // filterColumns.forEach(col => ids.push(col.id));

    return ids;
  }, [xAxisColumns, yAxisColumns, groupByColumns, filterColumns, colorByColumns, labelColumns]);

  // Update config when initialChartType changes (same as original)
  useEffect(() => {
    setConfig(prev => ({
      ...prev,
      type: validateChartType(initialConfig?.type) || initialChartType
    }));
  }, [initialConfig, initialChartType]);

  // Update config when chartConfig changes - using a ref to prevent infinite loops (adapted)
  const initialConfigRef = useRef(initialConfig);
  const configRef = useRef(config);

  useEffect(() => {
    // Store the current config in the ref
    configRef.current = config;
  }, [config]);

  // Create a flag to prevent multiple rapid updates
  const isUpdating = useRef(false);

  // Sync drop zones with config
  useEffect(() => {
    // Initialize X axis
    if (config.xAxis && xAxisColumns.length === 0) {
      const xAxisCol = draggableColumns.find(col => col.id === config.xAxis);
      if (xAxisCol) {
        setXAxisColumns([xAxisCol]);
      }
    }

    // Initialize Y axis
    if (config.yAxis && yAxisColumns.length === 0) {
      const yAxisCol = draggableColumns.find(col => col.id === config.yAxis);
      if (yAxisCol) {
        setYAxisColumns([yAxisCol]);
      }
    }

    // Initialize Group By
    if (config.groupBy && config.groupBy !== 'none' && groupByColumns.length === 0) {
      const groupByCol = draggableColumns.find(col => col.id === config.groupBy);
      if (groupByCol) {
        setGroupByColumns([groupByCol]);
      }
    }
  }, [config, draggableColumns, xAxisColumns.length, yAxisColumns.length, groupByColumns.length]);

  // Sync config with drop zones - this is the key function that updates the chart config
  // when items are dragged and dropped
  useEffect(() => {
    if (isUpdating.current) return;

    // Only update when we have valid dropzone contents
    if (xAxisColumns.length === 0 || yAxisColumns.length === 0) return;

    isUpdating.current = true;

    const newConfig = {
      ...config,
      xAxis: xAxisColumns[0]?.id || config.xAxis,
      yAxis: yAxisColumns[0]?.id || config.yAxis,
      groupBy: groupByColumns[0]?.id || 'none',
      customLabel: labelColumns[0]?.id || config.customLabel
    };

    // Update multi-series config based on Y-axis columns
    if (yAxisColumns.length > 1) {
      newConfig.multiSeries = true;

      // Create or update series based on columns
      const newSeries = yAxisColumns.map((col, index) => {
        // Try to find existing series for this column
        const existingSeries = series.find(s => s.field === col.id);

        if (existingSeries) {
          return existingSeries;
        }

        // Create new series
        return {
          id: `series-${Date.now()}-${index}`,
          field: col.id,
          color: COLORS[index % COLORS.length],
          label: col.name,
          visible: true
        };
      });

      setSeries(newSeries);
    } else {
      newConfig.multiSeries = false;
    }

    // Update config and notify parent
    setConfig(newConfig);

    if (onConfigChange) {
      onConfigChange(newConfig);
    }

    setTimeout(() => {
      isUpdating.current = false;
    }, 100);
  }, [xAxisColumns, yAxisColumns, groupByColumns, labelColumns]);

  // Handler for dropping items in drop zones - optimized for performance
  const handleItemDrop = (item: DraggableItem, zoneId: string) => {
    // Use requestAnimationFrame to improve performance during drag operations
    requestAnimationFrame(() => {
      switch (zoneId) {
        case 'x-axis':
          setXAxisColumns([item]);
          break;
        case 'y-axis':
          // Allow any column type for Y-axis, including strings
          if (config.type === 'pie') {
            // For pie charts, only allow one Y-axis
            setYAxisColumns([item]);
          } else {
            // For other charts, allow multiple Y-axes for multi-series
            // Don't add duplicates
            if (!yAxisColumns.some(col => col.id === item.id)) {
              setYAxisColumns(prev => [...prev, item]);
            }
          }
          break;
        case 'group-by':
          setGroupByColumns([item]);
          break;
        case 'filter':
          setFilterColumns(prev => [...prev, item]);

          // Create a new filter only if one doesn't already exist for this column
          if (!filters.some(f => f.column === item.id)) {
            setFilters(prev => [
              ...prev,
              {
                column: item.id,
                operator: 'equals',
                value: '',
                enabled: true
              }
            ]);
          }
          break;
        case 'color-by':
          setColorByColumns([item]);
          break;
        case 'label':
          setLabelColumns([item]);

          // Update custom label
          handleConfigChange({ customLabel: item.name });
          break;
      }
    });
  };

  // Handler for removing items from drop zones - optimized for performance
  const handleItemRemove = (itemId: string, zoneId: string) => {
    // Use requestAnimationFrame to improve performance during drag operations
    requestAnimationFrame(() => {
      switch (zoneId) {
        case 'x-axis':
          setXAxisColumns([]);
          break;
        case 'y-axis':
          setYAxisColumns(prev => prev.filter(item => item.id !== itemId));
          break;
        case 'group-by':
          setGroupByColumns([]);
          handleConfigChange({ groupBy: 'none' });
          break;
        case 'filter':
          setFilterColumns(prev => prev.filter(item => item.id !== itemId));

          // Remove associated filters
          setFilters(prev => prev.filter(f => f.column !== itemId));
          break;
        case 'color-by':
          setColorByColumns([]);
          break;
        case 'label':
          setLabelColumns([]);
          handleConfigChange({ customLabel: '' });
          break;
      }
    });
  };

  // Update handleConfigChange function to prevent update loops (same as original)
  const handleConfigChange = (newPartialConfig: Partial<ChartConfig>) => {
    if (isUpdating.current) return;

    isUpdating.current = true;

    // Create the updated config
    const updatedConfig = { ...config, ...newPartialConfig };

    // Update local state
    setConfig(updatedConfig);

    // Notify parent if callback exists, but only if the config has actually changed
    if (onConfigChange && JSON.stringify(updatedConfig) !== JSON.stringify(config)) {
      // Use a debounced approach to reduce update frequency
      const timeoutId = setTimeout(() => {
        onConfigChange(updatedConfig);
        isUpdating.current = false;
      }, 50); // Small delay to batch updates

      // Cleanup function to prevent memory leaks
      return () => {
        clearTimeout(timeoutId);
        isUpdating.current = false;
      };
    } else {
      isUpdating.current = false;
    }
  };

  // Apply filters to data
  const filteredData = useMemo(() => {
    if (!filters.length) return safeData;

    return safeData.filter(row => {
      return filters.every(filter => {
        if (!filter.enabled) return true;

        const value = row[filter.column];

        switch (filter.operator) {
          case 'equals':
            return value == filter.value;
          case 'notEquals':
            return value != filter.value;
          case 'greaterThan':
            return typeof value === 'number' && value > Number(filter.value);
          case 'lessThan':
            return typeof value === 'number' && value < Number(filter.value);
          case 'contains':
            return typeof value === 'string' &&
                  value.toLowerCase().includes(String(filter.value).toLowerCase());
          default:
            return true;
        }
      });
    });
  }, [safeData, filters]);

  // Apply aggregations to data (same as original)
  const processedData = useMemo(() => {
    if (config.aggregation === 'none' && (config.groupBy === 'none' || !config.groupBy)) {
      return filteredData;
    }

    // Group data
    const groupedData: Record<string, any[]> = {};

    filteredData.forEach(row => {
      // Use 'all' as the group key when groupBy is 'none' or empty
      const groupKey = config.groupBy && config.groupBy !== 'none'
        ? String(row[config.groupBy])
        : 'all';

      if (!groupedData[groupKey]) {
        groupedData[groupKey] = [];
      }
      groupedData[groupKey].push(row);
    });

    // Apply aggregation
    return Object.entries(groupedData).map(([key, rows]) => {
      const result: Record<string, any> = {};

      // Set group by value
      if (config.groupBy && config.groupBy !== 'none') {
        result[config.groupBy] = key;
      }

      // Set x-axis value if different from group by
      if (config.xAxis !== config.groupBy) {
        result[config.xAxis] = rows[0]?.[config.xAxis] || key;
      }

      // Get all fields needed for multi-series
      const fieldsToAggregate = config.multiSeries
        ? series.map(s => s.field)
        : [config.yAxis];

      // Apply aggregation function to all required numeric columns
      if (config.aggregation !== 'none') {
        fieldsToAggregate.forEach(field => {
          const values = rows.map((r: Record<string, any>) => {
            const val = r[field];
            // Handle various non-numeric cases
            if (val === null || val === undefined || val === '') return NaN;

            // If it's already a number, return it
            if (typeof val === 'number') return val;

            // If it's a string that can be converted to a number, convert it
            if (typeof val === 'string') {
              // Try to extract numeric part if it's a string with mixed content
              const numericMatch = val.match(/-?\d+(\.\d+)?/);
              if (numericMatch) {
                return Number(numericMatch[0]);
              }

              // For strings that don't contain numbers, use string length as a numeric representation
              return val.length;
            }

            // For other types, try to convert to number
            const num = Number(val);
            return num;
          }).filter((v: number) => !isNaN(v));

          switch (config.aggregation) {
            case 'sum':
              result[field] = values.reduce((sum: number, val: number) => sum + val, 0);
              break;
            case 'average':
              result[field] = values.length ?
                values.reduce((sum: number, val: number) => sum + val, 0) / values.length : 0;
              break;
            case 'min':
              result[field] = values.length ? Math.min(...values) : 0;
              break;
            case 'max':
              result[field] = values.length ? Math.max(...values) : 0;
              break;
            case 'count':
              result[field] = values.length;
              break;
          }
        });
      } else {
        // If no aggregation, use first row values
        if (rows.length > 0) {
          Object.keys(rows[0] || {}).forEach(col => {
            result[col] = rows[0][col];
          });
        }
      }

      return result;
    });
  }, [filteredData, config.aggregation, config.groupBy, config.yAxis, config.xAxis, config.multiSeries, series]);

  // Update a filter
  const updateFilter = (index: number, partialFilter: Partial<Filter>) => {
    const updatedFilters = [...filters];
    // Ensure operator is always valid
    if (partialFilter.operator) {
      partialFilter.operator = validateFilterOperator(partialFilter.operator);
    }
    updatedFilters[index] = { ...updatedFilters[index], ...partialFilter };
    setFilters(updatedFilters);
  };

  // Add this function to handle the imported configuration
  const handleImportConfig = (importedConfig: ChartConfigExport) => {
    // Update chart configuration with proper type validation
    setConfig({
      ...config,
      // Ensure we handle any missing properties with defaults and validate types
      type: validateChartType(importedConfig.config.type),
      xAxis: importedConfig.config.xAxis || config.xAxis,
      yAxis: importedConfig.config.yAxis || config.yAxis,
      title: importedConfig.config.title || config.title,
      description: importedConfig.config.description || config.description,
      color: importedConfig.config.color || config.color,
      showLegend: importedConfig.config.showLegend !== undefined ?
                  importedConfig.config.showLegend : config.showLegend,
      showLabels: importedConfig.config.showLabels !== undefined ?
                  importedConfig.config.showLabels : config.showLabels,
      showGrid: importedConfig.config.showGrid !== undefined ?
                importedConfig.config.showGrid : config.showGrid,
      aggregation: validateAggregationType(importedConfig.config.aggregation),
      groupBy: importedConfig.config.groupBy || config.groupBy,
      timeScale: validateTimeScaleType(importedConfig.config.timeScale),
      customLabel: importedConfig.config.customLabel || config.customLabel,
      enableZoom: importedConfig.config.enableZoom !== undefined ?
                  importedConfig.config.enableZoom : config.enableZoom,
      multiSeries: importedConfig.config.multiSeries !== undefined ?
                  importedConfig.config.multiSeries : config.multiSeries
    });

    // Update series if available
    if (importedConfig.series && importedConfig.series.length > 0) {
      setSeries(importedConfig.series);
    }

    // Update filters if available - validate each filter's operator
    if (importedConfig.filters && importedConfig.filters.length > 0) {
      const validatedFilters: Filter[] = importedConfig.filters.map(filter => ({
        column: filter.column,
        operator: validateFilterOperator(filter.operator),
        value: filter.value,
        enabled: filter.enabled
      }));
      setFilters(validatedFilters);
    }

    toast.success("Chart configuration imported successfully");
  }

  return (
    <div className={`chart-visualizer ${fullHeight ? 'h-full' : ''}`} data-color={config.color} data-label={config.customLabel}>
      <div className={`overflow-hidden ${!showConfig ? 'h-full flex flex-col' : ''}`}>
        {/* Compact Header with Chart Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
          <div>
            <h3 className="text-lg font-medium">{config.title}</h3>
            {config.description && (
              <p className="text-sm text-muted-foreground">{config.description}</p>
            )}
          </div>

          {/* Only show controls when showConfig is true */}
          {showConfig && (
            <div className="flex flex-wrap items-center gap-2">
              {/* Chart Type Selector */}
              <div className="flex gap-1">
                <Button
                  variant={config.type === 'line' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleConfigChange({ type: 'line' })}
                  title="Line Chart"
                >
                  <LineChartIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={config.type === 'bar' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleConfigChange({ type: 'bar' })}
                  title="Bar Chart"
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={config.type === 'pie' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleConfigChange({ type: 'pie' })}
                  title="Pie Chart"
                >
                  <PieChartIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={config.type === 'area' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleConfigChange({ type: 'area' })}
                  title="Area Chart"
                >
                  <TrendingUp className="h-4 w-4" />
                </Button>
              </div>

              {/* Visual Configuration Exporter */}
              <ChartConfigExporter
                config={config}
                series={series}
                filters={filters}
                data={processedData}
                onImport={handleImportConfig}
              />

              {/* Tabs Selection */}
              <Button
                variant={activeTab === 'chart' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
                onClick={() => setActiveTab('chart')}
              >
                <PanelTop className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Chart</span>
              </Button>
              <Button
                variant={activeTab === 'style' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
                onClick={() => setActiveTab('style')}
              >
                <Palette className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Style</span>
              </Button>
              <Button
                variant={activeTab === 'analytics' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
                onClick={() => setActiveTab('analytics')}
              >
                <ArrowUpDown className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Analytics</span>
              </Button>
              <Button
                variant={activeTab === 'filters' ? 'default' : 'outline'}
                size="sm"
                className={`h-8 ${filters.length > 0 ? 'bg-primary text-primary-foreground' : ''}`}
                onClick={() => setActiveTab('filters')}
              >
                <Filter className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">
                  {filters.length > 0 ? `Filters (${filters.length})` : 'Filters'}
                </span>
              </Button>
            </div>
          )}
        </div>

        {/* Drag and Drop Interface - only when showConfig is true */}
        {showConfig && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
            {/* Left Column - Available Columns */}
            <div className="border rounded-lg p-4">
              <h3 className="text-sm font-medium mb-2">Available Columns</h3>
              <ColumnsList
                columns={draggableColumns}
                usedColumnIds={usedColumnIds}
              />
            </div>

            {/* Middle Column - Chart Configuration */}
            <div className="border rounded-lg p-4">
              {activeTab === 'chart' && (
                <>
                  <h3 className="text-sm font-medium mb-3">Chart Layout</h3>
                  <div className="space-y-4">
                    <DropZone
                      id="x-axis"
                      title="X-Axis"
                      acceptTypes={['column']}
                      items={xAxisColumns}
                      onItemDrop={handleItemDrop}
                      onItemRemove={handleItemRemove}
                      isRequired={true}
                      maxItems={1}
                      icon={<PanelTop className="h-4 w-4" />}
                    />

                    <DropZone
                      id="y-axis"
                      title="Y-Axis"
                      acceptTypes={['column']}
                      items={yAxisColumns}
                      onItemDrop={handleItemDrop}
                      onItemRemove={handleItemRemove}
                      isRequired={true}
                      maxItems={config.type === 'pie' ? 1 : 5}
                      description={config.type !== 'pie' ? "Drag multiple columns for multi-series charts" : undefined}
                      icon={<ArrowUpDown className="h-4 w-4" />}
                    />

                    <DropZone
                      id="group-by"
                      title="Group By"
                      acceptTypes={['column']}
                      items={groupByColumns}
                      onItemDrop={handleItemDrop}
                      onItemRemove={handleItemRemove}
                      maxItems={1}
                      icon={<Layers className="h-4 w-4" />}
                    />

                    <DropZone
                      id="label"
                      title="Custom Label"
                      acceptTypes={['column']}
                      items={labelColumns}
                      onItemDrop={handleItemDrop}
                      onItemRemove={handleItemRemove}
                      maxItems={1}
                      icon={<Pin className="h-4 w-4" />}
                    />
                  </div>
                </>
              )}

              {activeTab === 'style' && (
                <>
                  <h3 className="text-sm font-medium mb-3">Chart Style</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Chart Color</Label>
                      <ColorPicker
                        value={config.color}
                        onChange={(color) => handleConfigChange({ color })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Title</Label>
                      <Input
                        value={config.title}
                        onChange={(e) => handleConfigChange({ title: e.target.value })}
                        placeholder="Chart Title"
                        className="h-8 text-xs"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Description</Label>
                      <Input
                        value={config.description}
                        onChange={(e) => handleConfigChange({ description: e.target.value })}
                        placeholder="Chart Description"
                        className="h-8 text-xs"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enable-zoom">Enable Zoom</Label>
                      <Switch
                        id="enable-zoom"
                        checked={config.enableZoom}
                        onCheckedChange={(checked) =>
                          handleConfigChange({ enableZoom: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="show-grid">Show Grid</Label>
                      <Switch
                        id="show-grid"
                        checked={config.showGrid}
                        onCheckedChange={(checked) =>
                          handleConfigChange({ showGrid: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="show-legend">Show Legend</Label>
                      <Switch
                        id="show-legend"
                        checked={config.showLegend}
                        onCheckedChange={(checked) =>
                          handleConfigChange({ showLegend: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="show-labels">Show Data Labels</Label>
                      <Switch
                        id="show-labels"
                        checked={config.showLabels}
                        onCheckedChange={(checked) =>
                          handleConfigChange({ showLabels: checked })
                        }
                      />
                    </div>
                  </div>
                </>
              )}

              {activeTab === 'analytics' && (
                <>
                  <h3 className="text-sm font-medium mb-3">Data Analysis</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Aggregation Method</Label>
                      <Select
                        value={config.aggregation}
                        onValueChange={(value: AggregationType) =>
                          handleConfigChange({ aggregation: value })
                        }
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="sum">Sum</SelectItem>
                          <SelectItem value="average">Average</SelectItem>
                          <SelectItem value="min">Minimum</SelectItem>
                          <SelectItem value="max">Maximum</SelectItem>
                          <SelectItem value="count">Count</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Time Scale (for date fields)</Label>
                      <Select
                        value={config.timeScale}
                        onValueChange={(value: TimeScaleType) =>
                          handleConfigChange({ timeScale: value })
                        }
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="day">Day</SelectItem>
                          <SelectItem value="week">Week</SelectItem>
                          <SelectItem value="month">Month</SelectItem>
                          <SelectItem value="year">Year</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </>
              )}

              {activeTab === 'filters' && (
                <>
                  <h3 className="text-sm font-medium mb-3">Filters</h3>

                  <DropZone
                    id="filter"
                    title="Drag columns here to create filters"
                    acceptTypes={['column']}
                    items={filterColumns}
                    onItemDrop={handleItemDrop}
                    onItemRemove={handleItemRemove}
                    maxItems={10}
                    icon={<Filter className="h-4 w-4" />}
                  />

                  {filters.length > 0 && (
                    <div className="mt-4 space-y-3">
                      <h4 className="text-sm font-medium">Configure Filters</h4>

                      <ScrollArea className="h-[200px]">
                        <div className="space-y-3 pr-2">
                          {filters.map((filter, index) => (
                            <div key={index} className="space-y-2 border rounded-md p-2">
                              <div className="flex items-center justify-between">
                                <Label className="text-sm">{filter.column}</Label>
                                <Switch
                                  checked={filter.enabled}
                                  onCheckedChange={(checked) =>
                                    updateFilter(index, { enabled: checked })
                                  }
                                />
                              </div>

                              <div className="grid grid-cols-2 gap-2">
                                <Select
                                  value={filter.operator}
                                  onValueChange={(value: FilterOperator) =>
                                    updateFilter(index, { operator: value })
                                  }
                                >
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="equals">=</SelectItem>
                                    <SelectItem value="notEquals">≠</SelectItem>
                                    <SelectItem value="greaterThan">&gt;</SelectItem>
                                    <SelectItem value="lessThan">&lt;</SelectItem>
                                    <SelectItem value="contains">Contains</SelectItem>
                                  </SelectContent>
                                </Select>

                                <Input
                                  value={filter.value}
                                  onChange={(e) =>
                                    updateFilter(index, { value: e.target.value })
                                  }
                                  placeholder="Value"
                                  className="h-8 text-xs"
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Right Column - Preview */}
            <div className="border rounded-lg p-4">
              <h3 className="text-sm font-medium mb-3">Preview</h3>
              <div className="aspect-video">
                {xAxisColumns.length > 0 && yAxisColumns.length > 0 ? (
                  config.multiSeries && config.type !== 'pie' ? (
                    <MultiSeriesChart
                      data={processedData}
                      chartType={config.type as 'line' | 'bar' | 'area'}
                      xAxis={config.xAxis}
                      series={series}
                      showGrid={config.showGrid}
                      showLegend={config.showLegend}
                      showLabels={config.showLabels}
                      enableZoom={config.enableZoom}
                    />
                  ) : (
                    <ZoomableChart
                      data={processedData}
                      chartType={config.type}
                      xAxis={config.xAxis}
                      yAxis={config.yAxis}
                      color={config.color}
                      showGrid={config.showGrid}
                      showLegend={config.showLegend}
                      showLabels={config.showLabels}
                      customLabel={config.customLabel || config.yAxis}
                      isZoomable={config.enableZoom}
                    />
                  )
                ) : (
                  <div className="flex items-center justify-center h-full bg-muted/20 rounded-md">
                    <p className="text-muted-foreground text-sm">
                      Drag columns to X and Y axis dropzones to create a chart
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Chart View - Chart should always be rendered with proper dimensions */}
        {!showConfig && (
          <div className="w-full flex-1" style={{ minHeight: fullHeight ? '100%' : '250px' }}>
            {processedData.length > 0 ? (
              <div className="w-full h-full" style={{ minHeight: fullHeight ? '100%' : '250px' }}>
                {config.multiSeries && config.type !== 'pie' ? (
                  <MultiSeriesChart
                    data={processedData}
                    chartType={config.type as 'line' | 'bar' | 'area'}
                    xAxis={config.xAxis}
                    series={series}
                    showGrid={config.showGrid}
                    showLegend={config.showLegend}
                    showLabels={config.showLabels}
                    enableZoom={config.enableZoom}
                  />
                ) : (
                  <ZoomableChart
                    data={processedData}
                    chartType={config.type}
                    xAxis={config.xAxis}
                    yAxis={config.yAxis}
                    color={config.color}
                    showGrid={config.showGrid}
                    showLegend={config.showLegend}
                    showLabels={config.showLabels}
                    customLabel={config.customLabel || config.yAxis}
                    isZoomable={config.enableZoom}
                  />
                )}
              </div>
            ) : (
              <div className="p-8 text-center text-muted-foreground bg-muted/20 rounded-md">
                No data available with current filters and settings
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}