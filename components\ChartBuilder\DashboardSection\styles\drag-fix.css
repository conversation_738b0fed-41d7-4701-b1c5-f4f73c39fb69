/**
 * Drag Fix CSS
 * 
 * This file provides critical fixes for react-grid-layout's drag and drop
 * functionality, ensuring items follow the cursor exactly without jumping.
 */

.react-grid-layout {
  position: relative;
}

.react-grid-item {
  /* Use absolute positioning */
  position: absolute;
  
  /* Disable all transitions */
  transition: none !important;
  
  /* Force GPU acceleration but avoid transforms */
  will-change: left, top;
  
  /* Ensure transparent background so overlapping works properly */
  background: transparent;
}

.react-grid-item.react-draggable-dragging {
  /* Higher z-index than other items */
  z-index: 9999 !important;
  
  /* Force cursor */
  cursor: grabbing !important;
  
  /* Ensure no transitions or transforms */
  transition: none !important;
  transform: none !important;
  
  /* Make item slightly visible through it */
  opacity: 0.9;
  
  /* Add a subtle shadow for visual feedback */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Fix drag handle to ensure it captures mouse events properly */
.draggable-handle {
  cursor: grab !important;
  touch-action: none !important;
  -webkit-user-select: none !important;
  user-select: none !important;
}

/* Remove transitions from the placeholder */
.react-grid-placeholder {
  transition: none !important;
  z-index: -1 !important;
  opacity: 0.2 !important;
  border-radius: 8px !important;
}

/* Prevent text selection during drag operations */
.react-grid-layout * {
  -webkit-user-select: none !important;
  user-select: none !important;
} 