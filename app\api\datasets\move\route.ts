import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/db";

// POST - Move a dataset to a folder
export async function POST(req: Request) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    const { datasetId, folderId } = await req.json();

    if (!datasetId) {
      return NextResponse.json(
        { error: "Dataset ID is required" },
        { status: 400 }
      );
    }

    // Check if dataset exists and belongs to the user
    const dataset = await prisma.dataSet.findUnique({
      where: { id: datasetId }
    });

    if (!dataset) {
      return NextResponse.json(
        { error: "Dataset not found" },
        { status: 404 }
      );
    }

    if (dataset.userId !== user.id) {
      return NextResponse.json(
        { error: "You don't have permission to move this dataset" },
        { status: 403 }
      );
    }

    // If folderId is provided, check if folder exists and belongs to the user
    if (folderId) {
      const folder = await prisma.dataSetFolder.findUnique({
        where: { id: folderId }
      });

      if (!folder) {
        return NextResponse.json(
          { error: "Folder not found" },
          { status: 404 }
        );
      }

      if (folder.userId !== user.id) {
        return NextResponse.json(
          { error: "You don't have permission to access this folder" },
          { status: 403 }
        );
      }
    }

    // Move dataset to folder (or to root if folderId is null)
    const updatedDataset = await prisma.dataSet.update({
      where: { id: datasetId },
      data: {
        folderId: folderId || null
      }
    });

    return NextResponse.json({
      success: true,
      dataset: updatedDataset
    });
  } catch (error) {
    console.error('Error moving dataset:', error);
    return NextResponse.json(
      { error: 'Failed to move dataset' },
      { status: 500 }
    );
  }
}

// POST - Move a folder to another folder
export async function PUT(req: Request) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    const { folderId, parentId } = await req.json();

    if (!folderId) {
      return NextResponse.json(
        { error: "Folder ID is required" },
        { status: 400 }
      );
    }

    // Check if folder exists and belongs to the user
    const folder = await prisma.dataSetFolder.findUnique({
      where: { id: folderId }
    });

    if (!folder) {
      return NextResponse.json(
        { error: "Folder not found" },
        { status: 404 }
      );
    }

    if (folder.userId !== user.id) {
      return NextResponse.json(
        { error: "You don't have permission to move this folder" },
        { status: 403 }
      );
    }

    // Prevent moving a folder into itself or its descendants
    if (parentId) {
      // Check if target folder exists and belongs to the user
      const targetFolder = await prisma.dataSetFolder.findUnique({
        where: { id: parentId }
      });

      if (!targetFolder) {
        return NextResponse.json(
          { error: "Target folder not found" },
          { status: 404 }
        );
      }

      if (targetFolder.userId !== user.id) {
        return NextResponse.json(
          { error: "You don't have permission to access the target folder" },
          { status: 403 }
        );
      }

      // Check if target folder is the folder itself or a descendant
      if (parentId === folderId) {
        return NextResponse.json(
          { error: "Cannot move a folder into itself" },
          { status: 400 }
        );
      }

      // Check if target folder is a descendant of the folder
      let currentFolder = targetFolder;
      while (currentFolder.parentId) {
        if (currentFolder.parentId === folderId) {
          return NextResponse.json(
            { error: "Cannot move a folder into its descendant" },
            { status: 400 }
          );
        }

        currentFolder = await prisma.dataSetFolder.findUnique({
          where: { id: currentFolder.parentId }
        }) as any;
      }
    }

    // Move folder to parent folder (or to root if parentId is null)
    const updatedFolder = await prisma.dataSetFolder.update({
      where: { id: folderId },
      data: {
        parentId: parentId || null
      }
    });

    return NextResponse.json({
      success: true,
      folder: updatedFolder
    });
  } catch (error) {
    console.error('Error moving folder:', error);
    return NextResponse.json(
      { error: 'Failed to move folder' },
      { status: 500 }
    );
  }
}
