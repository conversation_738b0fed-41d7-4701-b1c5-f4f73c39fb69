import React, { useState, useEffect, useRef } from 'react';
import { useAgentSetRag, AgentSetSearchResult, AgentSetChatMessage } from '@/hooks/useAgentSetRag';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Send, RefreshCw, Search, BrainCircuit, Sparkles, Settings, Maximize2 } from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { <PERSON>lider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import ModelSelector from './ModelSelector';
import ModernChatMessage from './ModernChatMessage';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Component for displaying search results
const SearchResults = ({ results }: { results: AgentSetSearchResult[] }) => {
  if (results.length === 0) {
    return (
      <div className="flex items-center justify-center h-40 text-muted-foreground">
        No results found
      </div>
    );
  }

  return (
    <ScrollArea className="h-[500px] pr-4">
      {results.map((result) => (
        <Card key={result.id} className="mb-4">
          <CardHeader className="py-3">
            <div className="flex justify-between items-center">
              <Badge variant="outline">{result.metadata?.filename || 'Unknown source'}</Badge>
              <Badge variant="secondary">Score: {result.score.toFixed(2)}</Badge>
            </div>
          </CardHeader>
          <CardContent className="py-2">
            <p className="text-sm whitespace-pre-wrap">{result.text}</p>
          </CardContent>
          <CardFooter className="py-2 text-xs text-muted-foreground">
            {result.metadata && (
              <div className="w-full">
                {result.metadata.filetype && <span>Type: {result.metadata.filetype} • </span>}
                {result.metadata.sequence_number !== undefined && <span>Sequence: {result.metadata.sequence_number}</span>}
              </div>
            )}
          </CardFooter>
        </Card>
      ))}
    </ScrollArea>
  );
};

// Component for displaying chat messages
const ChatMessages = ({ messages }: { messages: AgentSetChatMessage[] }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (messages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-[500px] text-muted-foreground">
        <Sparkles className="h-12 w-12 mb-4 text-muted-foreground/50" />
        <h3 className="text-lg font-medium mb-2">No messages yet</h3>
        <p className="text-sm text-center max-w-md">
          Start a conversation with AgentSet RAG to search and chat with your documents.
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[500px] pr-4">
      <div className="py-4 space-y-6">
        {messages.map((message, index) => (
          <ModernChatMessage
            key={index}
            message={message}
            isLast={index === messages.length - 1}
          />
        ))}
      </div>
      <div ref={messagesEndRef} />
    </ScrollArea>
  );
};

// Main AgentSet Chat Interface component
const AgentSetChatInterface = () => {
  const {
    namespaces,
    selectedNamespace,
    searchResults,
    isLoading,
    error,
    chatMessages,
    selectedModel,
    isReasoningMode,
    maxTokens,
    fetchNamespaces,
    searchNamespace,
    setSelectedNamespace,
    setSelectedModel,
    setIsReasoningMode,
    setMaxTokens,
    clearChat,
    sendMessage
  } = useAgentSetRag();

  const [searchQuery, setSearchQuery] = useState('');
  const [chatInput, setChatInput] = useState('');
  const [activeTab, setActiveTab] = useState('chat');

  // Using our dedicated AgentSet chat API endpoint

  useEffect(() => {
    fetchNamespaces();
  }, []);

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    if (!selectedNamespace) {
      toast.error('Please select a namespace first');
      return;
    }

    await searchNamespace(selectedNamespace, {
      query: searchQuery,
      topK: 10,
      rerank: true,
      includeMetadata: true
    });
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return;
    if (!selectedNamespace) {
      toast.error('Please select a namespace first');
      return;
    }

    const message = chatInput;
    setChatInput('');
    await sendMessage(message);
  };

  const handleRefresh = () => {
    fetchNamespaces();
    toast.success('Namespaces refreshed');
  };

  const handleClearChat = () => {
    clearChat();
    toast.success('Chat cleared');
  };

  return (
    <div className="container mx-auto p-4">
      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>AgentSet RAG Chat</CardTitle>
              <CardDescription>Search and chat with your documents using AgentSet</CardDescription>
            </div>
            <Button variant="outline" size="icon" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-4 mt-4">
            <Select value={selectedNamespace || ''} onValueChange={setSelectedNamespace}>
              <SelectTrigger className="w-[300px]">
                <SelectValue placeholder="Select a namespace" />
              </SelectTrigger>
              <SelectContent>
                {namespaces.map((namespace) => (
                  <SelectItem key={namespace.id} value={namespace.id}>
                    {namespace.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <ModelSelector value={selectedModel} onChange={setSelectedModel} />
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="chat">Chat</TabsTrigger>
              <TabsTrigger value="search">Search</TabsTrigger>
            </TabsList>
            <TabsContent value="chat" className="mt-4">
              <ChatMessages messages={chatMessages} />
              <div className="flex flex-col gap-4 mt-4">
                <div className="flex items-center gap-2">
                  {/* Reasoning Mode Toggle */}
                  <Button
                    type="button"
                    variant={isReasoningMode ? "secondary" : "outline"}
                    size="sm"
                    onClick={() => setIsReasoningMode(!isReasoningMode)}
                    disabled={isLoading}
                    className={cn(
                      "h-9 px-3 gap-1.5",
                      isReasoningMode && "bg-secondary text-secondary-foreground"
                    )}
                  >
                    <BrainCircuit className={cn("h-4 w-4", isReasoningMode && "text-primary")} />
                    <span className="text-xs font-medium">
                      {isReasoningMode ? "Reasoning On" : "Reasoning Off"}
                    </span>
                  </Button>

                  {/* Advanced Settings */}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="h-9 px-3 gap-1.5">
                        <Settings className="h-4 w-4" />
                        <span className="text-xs font-medium">Settings</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80">
                      <div className="space-y-4">
                        <h4 className="font-medium text-sm">Response Settings</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <label className="text-sm" htmlFor="max-tokens">
                              Max Response Length
                            </label>
                            <span className="text-xs text-muted-foreground">
                              {maxTokens} tokens
                            </span>
                          </div>
                          <Slider
                            id="max-tokens"
                            min={512}
                            max={4096}
                            step={512}
                            value={[maxTokens]}
                            onValueChange={(value) => setMaxTokens(value[0])}
                          />
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <label className="text-sm" htmlFor="reasoning-mode">
                              Reasoning Mode
                            </label>
                            <Switch
                              id="reasoning-mode"
                              checked={isReasoningMode}
                              onCheckedChange={setIsReasoningMode}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Enables detailed step-by-step reasoning and longer, more comprehensive responses with references.
                          </p>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="flex gap-2">
                  <Input
                    placeholder="Ask a question..."
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={isLoading}
                    className="px-4"
                  >
                    {isLoading ?
                      <Loader2 className="h-4 w-4 animate-spin mr-2" /> :
                      <Send className="h-4 w-4 mr-2" />
                    }
                    Send
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleClearChat}
                    disabled={isLoading}
                  >
                    Clear
                  </Button>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="search" className="mt-4">
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="Search query..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  disabled={isLoading}
                />
                <Button onClick={handleSearch} disabled={isLoading}>
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                </Button>
              </div>
              <Separator className="my-4" />
              <SearchResults results={searchResults} />
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between items-center text-xs text-muted-foreground">
          <span>Powered by AgentSet • {namespaces.length} namespaces available</span>
          <div className="flex items-center gap-3">
            {isReasoningMode && (
              <Badge variant="outline" className="flex items-center gap-1 py-0 h-5">
                <BrainCircuit className="h-3 w-3 text-primary" />
                <span>Reasoning</span>
              </Badge>
            )}
            <Badge variant="outline" className="flex items-center gap-1 py-0 h-5">
              <span>Model: {selectedModel}</span>
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1 py-0 h-5">
              <span>Max tokens: {maxTokens}</span>
            </Badge>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AgentSetChatInterface;
