// Define chart type options
export type ChartType =
  | 'line'
  | 'bar'
  | 'pie'
  | 'area'
  | 'scatter'
  | 'bubble'
  | 'heatmap'
  | 'radar'
  | 'boxplot'
  | 'candlestick'
  | 'funnel'
  | 'gauge'

// Define aggregation methods
export type AggregationType =
  | 'sum'
  | 'average'
  | 'min'
  | 'max'
  | 'count'
  | 'median'
  | 'mode'
  | 'stddev'
  | 'variance'
  | 'percentile25'
  | 'percentile75'
  | 'percentile90'
  | 'cumulative'
  | 'movingAverage'
  | 'none'

// Define time scaling options
export type TimeScaleType = 'day' | 'week' | 'month' | 'year' | 'none'

// Define filter operators
export type FilterOperator = 'equals' | 'notEquals' | 'greaterThan' | 'lessThan' | 'contains'

// Filter configuration
export interface Filter {
  column: string;
  operator: FilterOperator;
  value: string | number;
  enabled: boolean;
}

// Chart type styling configuration
export interface ChartTypeStyle {
  color: string;
  opacity: number;
  // Removed shape and size properties for more architect-focused design
}

// Chart configuration object
export interface ChartConfig {
  // Basic chart properties
  type: ChartType;
  chartTypes: ChartType[]; // Support for multiple chart types
  xAxis: string;
  xAxisColumns?: string[]; // Support for multiple X-axis columns
  yAxis: string;
  title: string;
  description: string;
  color: string; // Default color
  showLegend: boolean;
  showLabels: boolean;
  showGrid: boolean;

  // Visual properties
  opacity: number;

  // Chart type specific styling
  chartTypeStyles: {
    line?: ChartTypeStyle;
    bar?: ChartTypeStyle;
    pie?: ChartTypeStyle;
    area?: ChartTypeStyle;
  };

  // View options
  showAsTable: boolean; // Option to show data as table

  // Data processing
  aggregation: AggregationType;
  groupBy: string;
  timeScale: TimeScaleType;
  customLabel: string;

  // Interaction
  enableZoom: boolean;
  multiSeries: boolean;

  // Advanced analytics
  showTrendline: boolean;
  trendlineType: 'linear' | 'polynomial' | 'exponential' | 'logarithmic' | 'none';
  showOutliers: boolean;
  confidenceInterval: number;
  movingAveragePeriod: number;

  // Statistical indicators
  showMean: boolean;
  showMedian: boolean;
  showStdDev: boolean;

  // Comparison
  normalizeData: boolean;
  showPercentageChange: boolean;

  // Forecasting
  forecastPeriods: number;
  seasonalityPeriods: number;
}

// Data series for multi-series charts
export interface Series {
  id: string;
  field: string;
  color: string;
  label: string;
  visible: boolean;
}

// Chart visualizer props interface
export interface ChartVisualizerProps {
  data: Record<string, any>[] | undefined;
  initialChartType: ChartType;
  chartConfig: {
    // Basic chart properties
    xAxis: string;
    xAxisColumns?: string[]; // Support for multiple X-axis columns
    yAxis: string;
    title: string;
    description: string;
    showLegend: boolean;
    showLabels: boolean;
    showGrid: boolean;
    color?: string;
    customLabel?: string;
    type?: ChartType;
    chartTypes?: ChartType[]; // Support for multiple chart types

    // Visual properties
    opacity?: number;

    // View options
    showAsTable?: boolean; // Option to show data as table

    // Data processing
    aggregation?: string;
    groupBy?: string;
    timeScale?: string;

    // Interaction
    enableZoom?: boolean;
    multiSeries?: boolean;

    // Chart type specific styling
    chartTypeStyles?: {
      line?: { color?: string; opacity?: number };
      bar?: { color?: string; opacity?: number };
      pie?: { color?: string; opacity?: number };
      area?: { color?: string; opacity?: number };
    };

    // Advanced analytics
    showTrendline?: boolean;
    trendlineType?: string;
    showOutliers?: boolean;
    confidenceInterval?: number;
    movingAveragePeriod?: number;

    // Statistical indicators
    showMean?: boolean;
    showMedian?: boolean;
    showStdDev?: boolean;

    // Comparison
    normalizeData?: boolean;
    showPercentageChange?: boolean;

    // Forecasting
    forecastPeriods?: number;
    seasonalityPeriods?: number;
  };
  showConfig: boolean;
  fullHeight?: boolean;
  onConfigChange?: (newConfig: any) => void;
  cellId?: string;
  isDashboardChart?: boolean;
}

// Draggable item for drag-and-drop interface
export interface DraggableItem {
  id: string;
  name: string;
  type: string;
  dataType?: 'string' | 'number' | 'date' | 'boolean';
  color?: string;
  sourceZone?: string;
}

// Configuration export format
export interface ChartConfigExport {
  config: ChartConfig;
  series: Series[];
  filters: Filter[];
}