'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  MoreVertical,
  Edit2,
  Trash2,
  Copy,
  Calculator,
  Clock,
  CheckCircle,
  XCircle,
  Hash,
  GripVertical,
  Maximize2,
  TrendingUp,
  DollarSign,
  Users,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Star,
  Award,
  Briefcase,
  Calendar,
  FileText,
  Settings,
  Heart,
  Shield,
  Globe,
  Home,
  Mail,
  Phone,
  MapPin,
  Camera,
  Music,
  Video,
  Image,
  Book,
  Bookmark,
  Flag,
  Gift,
  Key,
  Lock,
  Unlock,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  Link,
  Bell,
  AlertCircle,
  Info,
  HelpCircle,
  Plus,
  Minus,
  X,
  Check,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  RotateCcw,
  RotateCw,
  Maximize,
  Minimize,
  Eye,
  EyeOff,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Send,
  Paperclip,
  Smile,
  Frown,
  Meh
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { CalculatorResultItem } from './types'
import { useDashboardStore } from '@/lib/dashboardStore'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import './styles/dashboard-chart-fix.css'

// Available icons for calculator cards
const AVAILABLE_ICONS = [
  { name: 'Calculator', icon: Calculator, color: 'text-blue-600' },
  { name: 'TrendingUp', icon: TrendingUp, color: 'text-green-600' },
  { name: 'DollarSign', icon: DollarSign, color: 'text-emerald-600' },
  { name: 'Users', icon: Users, color: 'text-purple-600' },
  { name: 'Target', icon: Target, color: 'text-red-600' },
  { name: 'BarChart3', icon: BarChart3, color: 'text-indigo-600' },
  { name: 'PieChart', icon: PieChart, color: 'text-pink-600' },
  { name: 'Activity', icon: Activity, color: 'text-orange-600' },
  { name: 'Zap', icon: Zap, color: 'text-yellow-600' },
  { name: 'Star', icon: Star, color: 'text-amber-600' },
  { name: 'Award', icon: Award, color: 'text-cyan-600' },
  { name: 'Briefcase', icon: Briefcase, color: 'text-slate-600' },
  { name: 'Calendar', icon: Calendar, color: 'text-teal-600' },
  { name: 'FileText', icon: FileText, color: 'text-gray-600' },
  { name: 'Settings', icon: Settings, color: 'text-stone-600' },
  { name: 'Heart', icon: Heart, color: 'text-rose-600' },
  { name: 'Shield', icon: Shield, color: 'text-blue-700' },
  { name: 'Globe', icon: Globe, color: 'text-green-700' },
  { name: 'Home', icon: Home, color: 'text-blue-500' },
  { name: 'Hash', icon: Hash, color: 'text-gray-500' }
]

interface CalculatorResultCardProps {
  item: CalculatorResultItem
  isEditMode?: boolean
  onUpdateCalculator?: (calculatorId: string, updates: Partial<CalculatorResultItem>) => void
  onRemoveCalculator?: (calculatorId: string) => void
  onToggleFullscreen?: (calculatorId: string) => void
  onEdit?: (item: CalculatorResultItem) => void
  onDelete?: (id: string) => void
}

export function CalculatorResultCard({
  item,
  isEditMode = false,
  onUpdateCalculator,
  onRemoveCalculator,
  onToggleFullscreen,
  onEdit,
  onDelete
}: CalculatorResultCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(item.title || '')
  const [editDescription, setEditDescription] = useState(item.description || '')
  const [selectedIcon, setSelectedIcon] = useState(item.icon || 'Calculator')
  const [isDragging, setIsDragging] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)
  const calculatorContainerRef = useRef<HTMLDivElement>(null)

  const { updateCalculatorResult, removeCalculatorResult } = useDashboardStore()

  // Function to trigger window resize
  const triggerResize = () => {
    window.dispatchEvent(new Event('resize'))
  }

  // Get icon component and color
  const getIconInfo = (iconName: string) => {
    const iconInfo = AVAILABLE_ICONS.find(icon => icon.name === iconName) || AVAILABLE_ICONS[0]
    return iconInfo
  }

  // Format the result for display
  const formatResult = (result: any, resultType?: string) => {
    if (result === null || result === undefined) return 'No result'

    if (resultType === 'error') return result

    if (typeof result === 'number') {
      // Format numbers with appropriate precision
      if (Number.isInteger(result)) return result.toString()
      if (Math.abs(result) >= 1000000) return result.toExponential(2)
      return result.toFixed(6).replace(/\.?0+$/, '')
    }

    return String(result)
  }

  // Get result type icon and color
  const getResultTypeInfo = (resultType?: string, result?: any) => {
    if (resultType === 'error' || (result === null && item.formula)) {
      return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-50 dark:bg-red-900/20' }
    }
    if (typeof result === 'number') {
      return { icon: Hash, color: 'text-blue-500', bgColor: 'bg-blue-50 dark:bg-blue-900/20' }
    }
    return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-50 dark:bg-green-900/20' }
  }

  const resultInfo = getResultTypeInfo(item.resultType, item.result)
  const ResultIcon = resultInfo.icon

  // Handle save edit
  const handleSaveEdit = () => {
    updateCalculatorResult(item.id, {
      title: editTitle.trim() || 'Calculator Result',
      description: editDescription.trim(),
      icon: selectedIcon
    })
    setIsEditing(false)
    toast.success('Calculator result updated')
  }

  // Handle delete
  const handleDelete = () => {
    removeCalculatorResult(item.id)
    onDelete?.(item.id)
    toast.success('Calculator result deleted')
  }

  // Copy result to clipboard
  const copyResult = () => {
    const textToCopy = `${item.formula} = ${formatResult(item.result, item.resultType)}`
    navigator.clipboard.writeText(textToCopy)
    toast.success('Result copied to clipboard')
  }

  // Copy formula to clipboard
  const copyFormula = () => {
    navigator.clipboard.writeText(item.formula)
    toast.success('Formula copied to clipboard')
  }

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  // Get current icon info
  const currentIconInfo = getIconInfo(item.icon || selectedIcon)
  const CurrentIcon = currentIconInfo.icon

  return (
    <div
      ref={cardRef}
      className={cn(
        "h-full w-full relative group",
        // No border in view mode, border in edit mode
        isEditMode
          ? "bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200 dark:border-blue-800 rounded-lg ring-2 ring-blue-300 dark:ring-blue-600"
          : "bg-transparent border-0",
        "transition-all duration-200",
        isDragging && "opacity-50 scale-95"
      )}
      data-calculator-id={item.id}
    >
      {/* Drag Handle - Only visible in edit mode */}
      {isEditMode && (
        <div className="absolute -top-2 -left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-blue-600 text-white p-1 rounded cursor-move drag-handle">
            <GripVertical className="h-3 w-3" />
          </div>
        </div>
      )}

      {/* Resize Handle - Only visible in edit mode */}
      {isEditMode && (
        <div className="absolute -bottom-2 -right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-blue-600 text-white p-1 rounded cursor-se-resize resize-handle">
            <Maximize2 className="h-3 w-3" />
          </div>
        </div>
      )}

      {/* VIEW MODE - Simple display with just title and result */}
      {!isEditMode && !isEditing ? (
        <div className="h-full flex flex-col justify-center items-center p-4 text-center">
          {/* Icon and Title */}
          <div className="flex flex-col items-center gap-2 mb-4">
            <CurrentIcon className={`h-8 w-8 ${currentIconInfo.color}`} />
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 line-clamp-2">
              {item.title || 'Calculator Result'}
            </h3>
          </div>

          {/* Result - Large and prominent */}
          <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {formatResult(item.result, item.resultType)}
          </div>
        </div>
      ) : (
        /* EDIT MODE - Full interface with all options */
        <Card className="h-full flex flex-col border-0 bg-transparent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-3 pt-3">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <CurrentIcon className={`h-4 w-4 ${currentIconInfo.color} flex-shrink-0`} />
              {isEditing ? (
                <Input
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  className="h-6 text-sm font-medium"
                  placeholder="Calculator Result"
                />
              ) : (
                <h3 className="text-sm font-semibold truncate text-blue-900 dark:text-blue-100">
                  {item.title || 'Calculator Result'}
                </h3>
              )}
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/50">
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem onClick={() => setIsEditing(!isEditing)}>
                  <Edit2 className="h-3 w-3 mr-2" />
                  {isEditing ? 'Cancel' : 'Edit'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={copyResult}>
                  <Copy className="h-3 w-3 mr-2" />
                  Copy Result
                </DropdownMenuItem>
                <DropdownMenuItem onClick={copyFormula}>
                  <Copy className="h-3 w-3 mr-2" />
                  Copy Formula
                </DropdownMenuItem>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-red-600">
                      <Trash2 className="h-3 w-3 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Calculator Result</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this calculator result? This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>

          <CardContent className="flex-1 px-3 pb-3 space-y-2">
            {/* Icon Selection - only show when editing */}
            {isEditing && (
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Choose Icon:</label>
                <div className="grid grid-cols-5 gap-2 max-h-32 overflow-y-auto">
                  {AVAILABLE_ICONS.map((iconInfo) => {
                    const IconComponent = iconInfo.icon
                    return (
                      <Button
                        key={iconInfo.name}
                        variant={selectedIcon === iconInfo.name ? "default" : "outline"}
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => setSelectedIcon(iconInfo.name)}
                      >
                        <IconComponent className={`h-4 w-4 ${iconInfo.color}`} />
                      </Button>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Description - only show if editing or has content */}
            {isEditing ? (
              <Textarea
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                placeholder="Add description..."
                className="text-xs resize-none"
                rows={2}
              />
            ) : (
              item.description && (
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {item.description}
                </p>
              )
            )}

            {/* Formula - only show in edit mode */}
            {isEditMode && (
              <div className="bg-white/60 dark:bg-gray-800/60 p-2 rounded border">
                <div className="text-xs text-muted-foreground mb-1">Formula:</div>
                <div className="font-mono text-xs text-blue-800 dark:text-blue-200 truncate">
                  {item.formula}
                </div>
              </div>
            )}

            {/* Result - prominent display */}
            <div className={`p-3 rounded-lg border-2 ${resultInfo.bgColor} ${
              item.resultType === 'error' ? 'border-red-300' : 'border-blue-300'
            }`}>
              <div className="flex items-center gap-2 mb-1">
                <ResultIcon className={`h-4 w-4 ${resultInfo.color}`} />
                <span className="text-sm font-semibold">Result</span>
              </div>
              <div className="font-mono text-xl font-bold text-center">
                {formatResult(item.result, item.resultType)}
              </div>
            </div>

            {/* Timestamp - compact */}
            {isEditMode && (
              <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>{formatTimestamp(item.timestamp)}</span>
              </div>
            )}

            {/* Edit Actions */}
            {isEditing && (
              <div className="flex gap-2 pt-2">
                <Button size="sm" onClick={handleSaveEdit} className="h-6 text-xs">
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  className="h-6 text-xs"
                >
                  Cancel
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
