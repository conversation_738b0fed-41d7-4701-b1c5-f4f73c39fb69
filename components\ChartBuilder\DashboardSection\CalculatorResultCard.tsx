import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  MoreVertical, 
  Edit2, 
  Trash2, 
  Copy, 
  Calculator,
  Clock,
  CheckCircle,
  XCircle,
  Hash
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { CalculatorResultItem } from './types'
import { useDashboardStore } from '@/lib/dashboardStore'
import { toast } from 'sonner'

interface CalculatorResultCardProps {
  item: CalculatorResultItem
  onEdit?: (item: CalculatorResultItem) => void
  onDelete?: (id: string) => void
}

export function CalculatorResultCard({ item, onEdit, onDelete }: CalculatorResultCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(item.title || '')
  const [editDescription, setEditDescription] = useState(item.description || '')
  
  const { updateCalculatorResult, removeCalculatorResult } = useDashboardStore()

  // Format the result for display
  const formatResult = (result: any, resultType?: string) => {
    if (result === null || result === undefined) return 'No result'
    
    if (resultType === 'error') return result
    
    if (typeof result === 'number') {
      // Format numbers with appropriate precision
      if (Number.isInteger(result)) return result.toString()
      if (Math.abs(result) >= 1000000) return result.toExponential(2)
      return result.toFixed(6).replace(/\.?0+$/, '')
    }
    
    return String(result)
  }

  // Get result type icon and color
  const getResultTypeInfo = (resultType?: string, result?: any) => {
    if (resultType === 'error' || (result === null && item.formula)) {
      return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-50 dark:bg-red-900/20' }
    }
    if (typeof result === 'number') {
      return { icon: Hash, color: 'text-blue-500', bgColor: 'bg-blue-50 dark:bg-blue-900/20' }
    }
    return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-50 dark:bg-green-900/20' }
  }

  const resultInfo = getResultTypeInfo(item.resultType, item.result)
  const ResultIcon = resultInfo.icon

  // Handle save edit
  const handleSaveEdit = () => {
    updateCalculatorResult(item.id, {
      title: editTitle.trim() || 'Calculator Result',
      description: editDescription.trim()
    })
    setIsEditing(false)
    toast.success('Calculator result updated')
  }

  // Handle delete
  const handleDelete = () => {
    removeCalculatorResult(item.id)
    onDelete?.(item.id)
    toast.success('Calculator result deleted')
  }

  // Copy result to clipboard
  const copyResult = () => {
    const textToCopy = `${item.formula} = ${formatResult(item.result, item.resultType)}`
    navigator.clipboard.writeText(textToCopy)
    toast.success('Result copied to clipboard')
  }

  // Copy formula to clipboard
  const copyFormula = () => {
    navigator.clipboard.writeText(item.formula)
    toast.success('Formula copied to clipboard')
  }

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Calculator className="h-4 w-4 text-blue-500 flex-shrink-0" />
          {isEditing ? (
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className="h-6 text-sm font-medium"
              placeholder="Calculator Result"
            />
          ) : (
            <h3 className="text-sm font-medium truncate">
              {item.title || 'Calculator Result'}
            </h3>
          )}
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <MoreVertical className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-40">
            <DropdownMenuItem onClick={() => setIsEditing(!isEditing)}>
              <Edit2 className="h-3 w-3 mr-2" />
              {isEditing ? 'Cancel' : 'Edit'}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={copyResult}>
              <Copy className="h-3 w-3 mr-2" />
              Copy Result
            </DropdownMenuItem>
            <DropdownMenuItem onClick={copyFormula}>
              <Copy className="h-3 w-3 mr-2" />
              Copy Formula
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete} className="text-red-600">
              <Trash2 className="h-3 w-3 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>

      <CardContent className="flex-1 space-y-3">
        {/* Description */}
        {isEditing ? (
          <Textarea
            value={editDescription}
            onChange={(e) => setEditDescription(e.target.value)}
            placeholder="Add description..."
            className="text-xs resize-none"
            rows={2}
          />
        ) : (
          item.description && (
            <p className="text-xs text-muted-foreground">
              {item.description}
            </p>
          )
        )}

        {/* Formula */}
        <div className="space-y-1">
          <div className="flex items-center gap-1">
            <span className="text-xs font-medium text-muted-foreground">Formula:</span>
            <Badge variant="outline" className="text-xs font-mono">
              {item.formula}
            </Badge>
          </div>
        </div>

        {/* Result */}
        <div className={`p-3 rounded-lg ${resultInfo.bgColor}`}>
          <div className="flex items-center gap-2 mb-1">
            <ResultIcon className={`h-4 w-4 ${resultInfo.color}`} />
            <span className="text-sm font-medium">Result</span>
          </div>
          <div className="font-mono text-lg font-bold">
            {formatResult(item.result, item.resultType)}
          </div>
        </div>

        {/* Timestamp */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span>{formatTimestamp(item.timestamp)}</span>
        </div>

        {/* Edit Actions */}
        {isEditing && (
          <div className="flex gap-2 pt-2">
            <Button size="sm" onClick={handleSaveEdit} className="h-6 text-xs">
              Save
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => setIsEditing(false)}
              className="h-6 text-xs"
            >
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
