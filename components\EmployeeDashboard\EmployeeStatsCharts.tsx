"use client"

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Users, Briefcase, UserCheck, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import EmployeeListModal from "./EmployeeListModal";

// Define colors for charts with semantic meaning
const CONTRACT_COLORS = {
  'CDI': '#0088FE',    // Blue for permanent contracts
  'CDD': '#00C49F',    // Green for fixed-term contracts
  'ANAPEC': '#FFBB28', // Yellow for ANAPEC contracts
  'Freelance': '#FF8042', // Orange for freelancers
  'Intern': '#8884D8',  // Purple for interns
  'Stagiaire': '#8884D8', // Purple for interns (French)
  'Part-time': '#82ca9d', // Light green for part-time
  'Temps partiel': '#82ca9d', // Light green for part-time (French)
  'Full-time': '#4CAF50', // Dark green for full-time
  'Temps plein': '#4CAF50', // Dark green for full-time (French)
  'Remote': '#9C27B0',   // Purple for remote work
  'Télétravail': '#9C27B0', // Purple for remote work (French)
  'Non assigné': '#cccccc', // Gray for unassigned
  'Unknown': '#cccccc'   // Gray for unknown
};

const GENDER_COLORS = {
  'Male': '#0088FE',    // Blue for male
  'Female': '#FF8042',  // Orange for female
  'Homme': '#0088FE',   // Blue for homme (French for male)
  'Femme': '#FF8042',   // Orange for femme (French for female)
  'Other': '#00C49F',   // Green for other
  'Autre': '#00C49F',   // Green for autre (French for other)
  'Non spécifié': '#cccccc', // Gray for non-specified
  'Unknown': '#cccccc'  // Gray for unknown
};

// Department colors will be assigned dynamically
const DEPARTMENT_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];

// Helper function to get color for contract type
const getContractColor = (type: string) => {
  return CONTRACT_COLORS[type as keyof typeof CONTRACT_COLORS] || CONTRACT_COLORS['Unknown'];
};

// Helper function to get color for gender
const getGenderColor = (gender: string) => {
  return GENDER_COLORS[gender as keyof typeof GENDER_COLORS] || GENDER_COLORS['Unknown'];
};

// Helper function to get color for department
const getDepartmentColor = (index: number) => {
  return DEPARTMENT_COLORS[index % DEPARTMENT_COLORS.length];
};

interface Employee {
  id: string;
  prenom: string;
  nom: string;
  poste: string;
  departement: string;
  salaire: number;
  dateDebut?: Date;
  genre?: string;
  typeEmploi?: string;
  contratType?: string | null;
}

interface EmployeeStatsChartsProps {
  contractTypeData: Array<{ name: string; value: number }>;
  genderData: Array<{ name: string; value: number }>;
  departmentData: Array<{ name: string; value: number }>;
  isLoading: boolean;
  allEmployees?: Employee[];
}

const EmptyState = () => (
  <Alert className="mb-4">
    <AlertCircle className="h-4 w-4" />
    <AlertDescription>
      No data available yet. Add some employees to see your dashboard statistics.
    </AlertDescription>
  </Alert>
);

const EmployeeStatsCharts: React.FC<EmployeeStatsChartsProps> = ({
  contractTypeData,
  genderData,
  departmentData,
  isLoading,
  allEmployees = []
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState('');
  const [selectedEmployees, setSelectedEmployees] = useState<Employee[]>([]);

  const [modalType, setModalType] = useState<'contract' | 'gender' | 'department' | 'default'>('default');

  const openEmployeeModal = (
    title: string,
    description: string,
    filterFn: (employee: Employee) => boolean,
    type: 'contract' | 'gender' | 'department' | 'default' = 'default'
  ) => {
    const filteredEmployees = allEmployees.filter(filterFn);
    setModalTitle(title);
    setModalDescription(description);
    setSelectedEmployees(filteredEmployees);
    setModalType(type);
    setModalOpen(true);
  };
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="w-full">
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="w-full h-[200px]" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-4 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Show empty state if no data
  if ((!contractTypeData || contractTypeData.length === 0) &&
      (!genderData || genderData.length === 0) &&
      (!departmentData || departmentData.length === 0)) {
    return <EmptyState />;
  }

  return (
    <div className="grid grid-cols-1 gap-3">
      {/* Contract Type Distribution Chart */}
      <Card className="w-full">
        <CardHeader className="p-2">
          <CardTitle className="text-xs flex items-center">
            <Briefcase className="mr-1 h-3 w-3 text-blue-500" /> Contract Types
          </CardTitle>
          <CardDescription className="text-[10px]">Distribution by contract type</CardDescription>
        </CardHeader>
        <CardContent className="p-2">
          <div className="w-full h-[180px] flex items-center justify-center">
            {contractTypeData && contractTypeData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={contractTypeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {contractTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={getContractColor(entry.name)} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [`${value} employees`, name]} />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-muted-foreground text-sm">No contract type data available</div>
            )}
          </div>
          {/* Color legend */}
          <div className="flex flex-wrap gap-1 mt-2 justify-center">
            {contractTypeData && contractTypeData.map((entry, index) => (
              <Badge key={index} variant="outline" className="text-[9px] flex items-center">
                <div
                  className="w-2 h-2 rounded-full mr-1"
                  style={{ backgroundColor: getContractColor(entry.name) }}
                />
                {entry.name}
              </Badge>
            ))}
          </div>
        </CardContent>
        <CardFooter className="text-[10px] text-muted-foreground p-1 flex flex-col">
          <div className="flex items-center">
            <Briefcase className="h-2 w-2 mr-1" /> Distribution of employees by contract type
          </div>
          <Button
            variant="outline"
            size="sm"
            className="mt-2 h-6 text-[10px]"
            onClick={() => {
              // Get the contract types from the chart data
              const contractTypes = contractTypeData.map(item => item.name);
              openEmployeeModal(
                'Contract Types',
                'View employees by contract type',
                (emp) => {
                  const empContractType = emp.contratType || emp.typeEmploi;
                  return Boolean(empContractType) && contractTypes.includes(empContractType);
                },
                'contract'
              );
            }}
          >
            <Eye className="h-2 w-2 mr-1" /> View All Employees
          </Button>
        </CardFooter>
      </Card>

      {/* Gender Distribution and Department Size in same grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Gender Distribution Chart */}
        <Card className="w-full">
          <CardHeader className="p-2">
            <CardTitle className="text-xs flex items-center">
              <Users className="mr-1 h-3 w-3 text-blue-500" /> Gender Distribution
            </CardTitle>
            <CardDescription className="text-[10px]">Male vs Female employees</CardDescription>
          </CardHeader>
          <CardContent className="p-2">
            <div className="w-full h-[180px] flex items-center justify-center">
              {genderData && genderData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={genderData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={70}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {genderData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getGenderColor(entry.name)} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value} employees`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-muted-foreground text-sm">No gender data available</div>
              )}
            </div>
            {/* Color legend */}
            <div className="flex flex-wrap gap-1 mt-2 justify-center">
              {genderData && genderData.map((entry, index) => (
                <Badge key={index} variant="outline" className="text-[9px] flex items-center">
                  <div
                    className="w-2 h-2 rounded-full mr-1"
                    style={{ backgroundColor: getGenderColor(entry.name) }}
                  />
                  {entry.name}
                </Badge>
              ))}
            </div>
          </CardContent>
          <CardFooter className="text-[10px] text-muted-foreground p-1 flex flex-col">
            <div className="flex items-center">
              <Users className="h-2 w-2 mr-1" /> Distribution of employees by gender
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 h-6 text-[10px]"
              onClick={() => {
                // Get the genders from the chart data
                const genders = genderData.map(item => item.name);
                openEmployeeModal(
                  'Gender Distribution',
                  'View employees by gender',
                  (emp) => Boolean(emp.genre) && genders.includes(emp.genre),
                  'gender'
                );
              }}
            >
              <Eye className="h-2 w-2 mr-1" /> View All Employees
            </Button>
          </CardFooter>
        </Card>

        {/* Department Size Chart */}
        <Card className="w-full">
          <CardHeader className="p-2">
            <CardTitle className="text-xs flex items-center">
              <UserCheck className="mr-1 h-3 w-3 text-blue-500" /> Department Size
            </CardTitle>
            <CardDescription className="text-[10px]">Employees per department</CardDescription>
          </CardHeader>
          <CardContent className="p-2">
            <div className="w-full h-[180px] flex items-center justify-center">
              {departmentData && departmentData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={departmentData}
                    margin={{
                      top: 5,
                      right: 5,
                      left: 0,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" tick={{ fontSize: 8 }} />
                    <YAxis tick={{ fontSize: 8 }} />
                    <Tooltip formatter={(value) => [`${value} employees`]} />
                    <Bar dataKey="value" fill="#8884d8">
                      {departmentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getDepartmentColor(index)} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-muted-foreground text-sm">No department data available</div>
              )}
            </div>
            {/* Color legend */}
            <div className="flex flex-wrap gap-1 mt-2 justify-center">
              {departmentData && departmentData.map((entry, index) => (
                <Badge key={index} variant="outline" className="text-[9px] flex items-center">
                  <div
                    className="w-2 h-2 rounded-full mr-1"
                    style={{ backgroundColor: getDepartmentColor(index) }}
                  />
                  {entry.name}
                </Badge>
              ))}
            </div>
          </CardContent>
          <CardFooter className="text-[10px] text-muted-foreground p-1 flex flex-col">
            <div className="flex items-center">
              <UserCheck className="h-2 w-2 mr-1" /> Number of employees in each department
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 h-6 text-[10px]"
              onClick={() => {
                // Get the departments from the chart data
                const departments = departmentData.map(item => item.name);
                openEmployeeModal(
                  'Department Distribution',
                  'View employees by department',
                  (emp) => Boolean(emp.departement) && departments.includes(emp.departement),
                  'department'
                );
              }}
            >
              <Eye className="h-2 w-2 mr-1" /> View All Employees
            </Button>
          </CardFooter>
        </Card>
      </div>
      <EmployeeListModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title={modalTitle}
        description={modalDescription}
        employees={selectedEmployees}
        modalType={modalType}
      />
    </div>
  );
};

export default EmployeeStatsCharts;
